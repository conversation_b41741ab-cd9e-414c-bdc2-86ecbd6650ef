#include "app_main.h"
#include "bsp_usart/bsp_usart.h"
#include "Usart3.h"
#include "HW.h"
#include "FreeRTOS.h"
#include "task.h"
#include "lv_gui.h"
#include "Serial.h"
#include "PC_COM.h"
#include "wen_lib.h"
#include "Keyboard.h"
#include "wheap.h"
#include "Meu.h"
#include "bsp_iwdg/bsp_iwdg.h" 
#include "bsp_systick/bsp_SysTick.h"
#include "mh2457.h"
#include "diskio.h"
#include "font_example.h"
#include "lfs_speed_test.h"
#include "malloc.h"




#define TEST_GPIO   0
#define TEST_PWM    0
#define TEST_FB     1

//播放下一幅图片
void call_full_play(WEN_event *e);
//播放指定图片
void call_full_pic_one(WEN_event *e);
//完成一次像素级滚动
void call_rolly_wellcome(WEN_event *e);
//处理按键信号
bool key_check();
void call_key_event(WEN_event *e);
//更新一次客显显示
void call_updata_text(WEN_event *e);
//显示二维码
void call_show_qr(WEN_event *e);
//显示点阵二维码
void call_show_qr_pix(WEN_event *e);

//extern render_t* render;

void soft_delay(uint32_t x)
{
	while(x--) {
	}
}


#define  user_flash_addr    0X64000      //用户flash 使用空间      0~0X64000  是程序空间

extern WEN_FIFO *fifo_sys_uart_rx;
volatile char sys_booting = 0;   //系统启动标志

char xmain[1024];

volatile u8 t_update_show = 0;
Event_list *e_pavo;


volatile u32 t_well_come = 0;
u8 t_guest = 0;



u16 time_sec;
u16 t_lun_bo;      // 进入轮播倒计时
u16 t_pic_switch;  // 轮播切换图片倒计时
int t_cursor = 0;


void key_reset(WEN_KEY *key);

void _sbrk()
{

}

char *pic_lop[Internal_picture_max] = { "P1.jpg", "P2.jpg", "P3.jpg", "P4.jpg", "P5.jpg", "P6.jpg", "P7.jpg", "P8.jpg", "P9.jpg", "P10.jpg", };





void end()
{

}



void event_timer_sec(void);    

char chip_id[32];

char *product_name;
char *mode[] = {"PD500-II","PD700-II"};


void null_fuc(){}	
	
extern char bank_chrg;  //POSbank配置下发标志	
	
char show[1024];


//===========================================================================================//

SYSTEM System;

//PC_COM_FLG *pcc; // 存放临时变量


//=========================事件驱动========================//
byte Bool_Recv_Err;
RS Rs_com0;
word RsTimeOut;
TX Tx_com0;



extern void hx_Comter_Run(void); 				// 客显解析

extern uint8_t Rxflag;
extern uint8_t ucaRxBuf[256];
extern uint16_t usRxCount; 


u32 ms_tick = 0;								// 3秒显示LOGO计时器


//============================FreeRtos任务以及自己定义==========================//
TaskHandle_t RunLvglHandle;
void RunLvgl_Init(void);
void Main_Task(void* param);

void RunLvgl_Create(void)
{
    BaseType_t ret;
    
    ret = xTaskCreate(Main_Task, "RunLvgl Task", 2048, NULL, 2, &RunLvglHandle);
    if (ret != pdPASS)
    {
        LOG_ERROR("MiniShell Task Create Error.\n");
    }
}



//extern unsigned char my_wheap_buf[];
//unsigned char my_wheap_buf[1024*1024*4]   __attribute__((section(".bss.ARM. ")));
//unsigned char my_wheap_buf[1024*1024*1]   __attribute__((section(".bss.ARM.__at_0x60400000")));


extern void dis_px(int x, int y, u16 c);			// 显示像素点(垂直方向)
extern void dis_px2(int x, int y, uint16_t c);		// 显示像素点(水平方向)

extern u8 flag_clear_screen;						// 显示屏幕清除标志


extern SCREEN_TEXT S_text;
extern SYSTEM pavo_sys;
extern SYSTEM_ASC system_asc;
extern FLASHING flashing;
extern CLOCK pavo_clock;


WEN_KEY key_m, key_up, key_down;			//菜单键(S2)，上键(S3)，下键(S3)

//=============================================================================//




//=================================LVGL显示====================================//

extern lv_obj_t *g_canvas;		
extern lv_color_t *cbuf;



//显示图片变量
extern const lv_img_dsc_t picture;  // 声明外部图片描述符
extern const uint8_t picture_map[]; // 声明图片数据数组

static TickType_t last_key_time = 0;

#define IDLE_TIMEOUT_MS 5000
//=============================================================================//







extern bool TestLITTLEFS(lfs_t *lfs);			// 文件系统测试
void Timer0_INTER_HANDLE(void);	
extern void M_Show_Char(P_Font *pf, int x, int y, int ch);

bool flag = false;


extern void lv_scroll_canvas(void);

extern void create_rolly_timer(void);








// 主任务实现
void Main_Task(void* param)
{
	static uint32_t count = 0; 
	static uint32_t rolly_timer = 0;
	
	// 扫描按键初始化
	KeyBoard_Init();
	// LVGL初始化
    RunLvgl_Init();		
	printf("RunLvgl Init OK\r\n");		
	// 关闭背光
	BL_Unit_Config(0);	
	// 开启背光
	vTaskDelay(3000);				//延迟3s
	BL_Unit_Config(100);
	// GUI初始化
	lv_gui_init();
	
	
	
	// 新建一个事件队列
	// e_pavo = event_new(30,null_fuc,null_fuc);				

	pcc = (PC_COM_FLG *)wheapMalloc(sizeof(PC_COM_FLG));
	if(!pcc) {
        return; 											// 内存分配失败处理
    }
	memset(pcc, 0, sizeof(PC_COM_FLG));
	

	Initsys(); 												// 客显初始化,写入客显到flash
	
	product_name = mode[pavo_sys.mode_type];				// 载入产品名
	
	

	// 显示图片(LOGO)
	/*
		1、从文件系统加载 logo.jpg 文件。
		2、通过 jpg 解码器解出 lvgl 图像
	*/
	// 延时3s显示LOGO
	uint32_t t1 = ms_tick;
	PAVO_Show_picture("Picture_logo.jpg", 0, 0, LCD_XSIZE_TFT, LCD_YSIZE_TFT);		
	while(ms_tick - t1 < 3000)
	{	
		lv_timer_handler();			
		vTaskDelay(2);		
	}

	
	if ( !pavo_sys.back_light || pavo_sys.back_light > 7 ) 
	{
		pavo_sys.back_light = 5;
	}
	delay_ms(200);	 
	set_back_light(pavo_sys.back_light);
	printf("pavo_sys.back_light 	= %d\n", pavo_sys.back_light);
	// 加载字库
	Load_font();	
	// 字体显示。
	//M_Show_Char(system_font, 0, 0, 'A');  //显示一个字母A，从下载下去的字库里面选择字体
	// 系统显示
	Display_system();
	printf("pavo_sys.temp1          = %x\r\n", pavo_sys.temp1);
	printf("pavo_sys.Char_No        = %x\r\n", pavo_sys.Char_No);
	printf("pavo_sys.Expand_Char_No = %x\r\n", pavo_sys.Expand_Char_No);
	//delay_ms(2000);
	// 延迟2s显示系统信息界面
	uint32_t t2 = ms_tick;
	while(ms_tick - t2 < 2000)
	{	
		lv_timer_handler();			
		vTaskDelay(2);		
	}
		
	
	// 初始化客显界面
	Init_scr();
	//delay_ms(2000);


//	key_reset(&key_m);
//	key_reset(&key_up);
//	key_reset(&key_down);
//	t_lun_bo = pavo_sys.into_lun_bo;      	//进入轮播倒计时
		


	// 初始化看门狗	
	if (RCC_GetFlagStatus(RCC_FLAG_IWDGRST) != RESET)
	{
		printf("Reset signal is IWDG \r\n");
		RCC_ClearFlag();
	}
	else
	{
		printf("Reset signal is not IWDG \r\n");
	}
	IWDG_Config(IWDG_Prescaler_64, 625);			// IWDG约0.5s超时溢出,即产生复位	

	
	while (1)
    {		
		/*************************************** 命令处理模块 **************************************/
		// 处理串口数据
		if (!is_empty())
		{
			Rece_comm();
		}
	
		lv_timer_handler();
		vTaskDelay(1);
    }


}


void RunLvgl_Init(void)
{
	lv_init();
	lv_port_disp_init();
	lv_port_indev_init();
}


uint32_t timer_test2 = 0;
void vApplicationTickHook() 	//1ms 运行一次。中断调用
{
	lv_tick_inc(1);
	Timer0_INTER_HANDLE();		
	timer_test2++;	
	ms_tick++;
}





/*************************************************************5寸移植函数区域*************************************************************/

u16 uart_reset_coute = 0;
void UART0_Received_byte_handle(char c)
{
/*
	if ( sys_booting ) FIFO_Write_Byte(fifo_sys_uart_rx, c);
	else {
		//检查启动
		if ( c == 0X07 ) {
			if ( 300 < uart_reset_coute++ ) {
				DogWatch_flg = 0;
			}
		} else uart_reset_coute = 0;

		FIFO_Write_Byte(fifo_sys_uart_rx, c);
	}
*/
}

void UART1_Received_byte_handle(char c)
{
	//Serial0(c);
}


volatile u32 delay_ms_timer;
extern char system_Writing_flg;
u32 temp_t;
u32 t_sec;
u16 t_dog;
void Timer0_INTER_HANDLE(void)  //引入定时器0的处理函数，1ms 一次中断
{
	
	
//	if ( delay_ms_timer ) delay_ms_timer--;
//	if ( !sys_booting ) {		//启动期间不运行用户程序

//		if ( t_update_show ) t_update_show--;
//		if ( t_well_come ) t_well_come--;
//		if ( time_out ) time_out--;
//		if ( demo_time_no ) demo_time_no--;
//		if ( time_sec < 0XFFFF ) time_sec++;
//		if ( t_cursor ) t_cursor--;

//		temp_t = read32(0x01C20800 + 0xA0);
//		KEY_C(&key_m, temp_t & (1 << 2) ? 1 : 0);
//		KEY_C(&key_up, temp_t & (1 << 3) ? 1 : 0);
//		KEY_C(&key_down, temp_t & (1 << 4) ? 1 : 0);

//		if ( ++t_sec >= 1000 / tick ) {
//			t_sec = 0;  //定时器1秒钟
//			if ( t_meu_clear ) t_meu_clear--;

//		}

//		event_timer_inc(e_pavo,2);

//		//客显时钟
//		if ( pavo_clock.flg ) {
//			pavo_clock.number++;
//			if ( pavo_clock.number >= 1000 / tick )    //0.5ms一次中断
//			{
//				pavo_clock.number = 0;
//				pavo_clock.seconds++;
//				if ( pavo_clock.seconds >= 60 ) {
//					pavo_clock.seconds = 0;
//					pavo_clock.minutes++;
//					if ( pavo_clock.minutes >= 60 ) {
//						pavo_clock.minutes = 0;
//						pavo_clock.hours++;
//						if ( pavo_clock.hours >= 24 ) pavo_clock.hours = 0;
//					}
//				}
//			}
//		}
//	}
	
	if (DogWatch_flg || system_Writing_flg) 	//系统保存期间。继续喂狗
	{  
		system_Writing_flg = 0;
		Wdog_Restart();
	}
	
}



void __fatal_error(const char *msg)
{
	while(1);
}


//播放下一幅图片
void call_full_play(WEN_event *e)
{
/*
	static u32 lun_idx = 0;
	if ( pavo_sys.pic_reg ) {
		while(!(pavo_sys.pic_reg & (1 << lun_idx))) {
			if ( ++lun_idx > 10 ) lun_idx = 0;
		}
		if ( lun_idx >= 10 ) lun_idx = 0;
		switch_paint_scr(scr_pic);
		PAVO_Show_picture(pic_lop[lun_idx], 0, 0, LCD_XSIZE_TFT, LCD_YSIZE_TFT);
		scr_to_show(scr_pic);
		lun_idx++;
	}
*/
}


// 播放指定图片
void call_full_pic_one(WEN_event *e)
{
/*
	//t_lun_bo=0;
	t_lun_bo = 30 + pavo_sys.into_lun_bo;      //进入轮播倒计时
	if ( e->value1 < Internal_picture_max + 1 && e->value1 > 0 ) {
		switch_paint_scr(scr_pic);
		PAVO_Show_picture(pic_lop[e->value1 - 1], 0, 0, LCD_XSIZE_TFT, LCD_YSIZE_TFT);
		scr_to_show(scr_pic);
		t_well_come = 60000 / tick;
	}
*/
}


extern u16 number_of_well;      // 有多少个像素需要滚动
u16 iex = 0;
// 完成一次像素级滚动，滚动内容来自  scr_full_pic
void call_rolly_wellcome(WEN_event *e)
{
	
}


// 按键检查
bool key_check()
{
/*
	//菜单键按下
	if ( key_m.press ) {
		if ( key_m.reset ) {
			key_m.press = 0;
			event_send(e_pavo, call_key_event, m_press, 0, 0, 0);
			return true;
		}
	}
	//菜单键长按
	if ( key_m.keep ) {
		key_m.press = 0;
		key_m.keep = 0;
		event_send(e_pavo, call_key_event, m_keep, 0, 0, 0);
		return true;
	}

	//up按下
	if ( key_up.press ) {
		if ( key_up.reset ) {
			key_up.press = 0;
			event_send(e_pavo, call_key_event, up_press, 0, 0, 0);
			return true;
		}
	}

	//down按下
	if ( key_down.press ) {
		if ( key_down.reset ) {
			key_down.press = 0;
			event_send(e_pavo, call_key_event, down_press, 0, 0, 0);
			return true;
		}
	}
*/
	return 0;
}



//切换更新客显正文
void call_updata_text(WEN_event *e)
{
/*
	if ( pavo_sys.style == 1 ) {
		if ( cur_show_page != scr_main ) {
			scr_to_show(scr_main);
		} else {
			//Show_Screen_area(scr_main, 0, 80, 800, 480 - 160);
		}
	}
	if ( pavo_sys.style == 2 ) {
		if ( pcc->qr_flg ) {
			pcc->qr_flg = 0;
			switch_paint_scr(scr_main);
			PAVO_Show_picture("Guest.jpg", 0, 120, LCD_XSIZE_TFT, LCD_YSIZE_TFT - 120); //显示数据写入缓冲
			cur_show_page = scr_main + 1;  //触发全屏显示
		}
		if ( cur_show_page != scr_main ) {
			scr_to_show(scr_main);
		} else {
			//Show_Screen_area(scr_main, 0, 0, 800, 120);
		}
	}

	if ( pavo_sys.style == 3 ) {
		if ( pcc->qr_flg ) {
			pcc->qr_flg = 0;
			switch_paint_scr(scr_main);
			PAVO_Show_picture("Guest.jpg", 0, 160, LCD_XSIZE_TFT, LCD_YSIZE_TFT - 160); //显示数据写入缓冲
			cur_show_page = scr_main + 1;  //触发全屏显示
		}
		if ( cur_show_page != scr_main ) {
			scr_to_show(scr_main);
		} else {
			//Show_Screen_area(scr_main, 0, 0, 800, 160);
		}
	}
*/
	
}


extern SCR *cur_paint_scr;    //当前操作的界面
//显示,在哪一个屏显示

void show_qrcode_pix(SCR * scr_where, int start_x, int start_y, int qr_size, char *text)
{
/*
	int cd = 0X80;
	for ( int y = start_y ; y < start_y + qr_size ; y++ ) {
		for ( int x = start_x ; x < start_x + qr_size ; x++ ) {
			if(*text & cd) cur_paint_scr->pix_dat[x + y * LCD_XSIZE_TFT] = 0;
			else cur_paint_scr->pix_dat[x + y * LCD_XSIZE_TFT] = 0XFFFF;
			cd >>=1;
			if(cd==0){
				cd=0X80;
				text++;
			}
		}
	}
*/
}




void call_show_qr_pix(WEN_event *e)
{
//	pcc->qr_flg = 1;
//	int qr_show_rand;   //显示二维码的大小
//	int qr_show_y;      //显示二维码的Y位置
//	int qr_show_xoset;  //二维码边缘位置
//	t_lun_bo = 60 + pavo_sys.into_lun_bo;      //进入轮播倒计时
//	pcc->scr_update = 0;
//	S_text.Change_flg = 0;
//	//install_putchar(sys_uart_putc);
//	// printf("pavo_sys.style = %d\n",pavo_sys.style);
//	if ( pavo_sys.style == 2 ) {
//		t_well_come = 20000 / tick;
//		qr_show_rand = 192;   //显示二维码的大小
//		qr_show_y = 220;    //显示二维码的Y位置
//		qr_show_xoset = 35;   //二维码边缘位置
//	}

//	if ( pavo_sys.style == 3 ) {
//		t_well_come = 20000 / tick;
//		qr_show_rand = 192;   //显示二维码的大小
//		qr_show_y = 220;    //显示二维码的Y位置
//		qr_show_xoset = 35;   //二维码边缘位置
//	}

//	if ( pavo_sys.style == 1 ) {
//		t_well_come = 20000 / tick;
//		qr_show_rand = 192;   //显示二维码的大小
//		qr_show_y = 115;    //显示二维码的Y位置
//		qr_show_xoset = 35;   //二维码边缘位置
//	}
//	S_text.Top_line_roll_flg = pavo_clock.display_flg = flashing.flag = S_text.Cursor_flg = 0;
//	//		pcc.t_int_to_lun_bo = set_qr_back_time + pavo_sys.into_lun_bo;	   //设定进入轮播的时间
//	//	  pcc.t_pic_switch=pavo_sys.lun_time;
//	//正文显示二维码
//	qr_show_rand = 192;   //显示二维码的大小


//	switch ( e->value1 )
//	{
//		case 1 :  //靠左
//			show_qrcode_pix(scr_main, qr_show_xoset, qr_show_y, qr_show_rand, e->mem);
//			break;
//		case 2 :
//			show_qrcode_pix(scr_main, (LCD_XSIZE_TFT - qr_show_rand) / 2, qr_show_y, qr_show_rand, e->mem);
//			break;
//		case 3 :
//			show_qrcode_pix(scr_main, LCD_XSIZE_TFT - qr_show_rand - qr_show_xoset, qr_show_y, qr_show_rand, e->mem);
//			break;
//		default :
//			show_qrcode_pix(scr_main, (LCD_XSIZE_TFT - qr_show_rand) / 2, qr_show_y, qr_show_rand, e->mem);
//			break;
//	}
//	scr_to_show(scr_main);

//	//先显示图片再显示二维码
//	Read_Byte_UART();
//	Read_Byte_UART();
//	Read_Byte_UART();
//	//	delay_ms(1000);
//	if ( e->mem ) free(e->mem);
//	
}


void call_show_qr(WEN_event *e)
{
/*
	pcc->qr_flg = 1;
	int qr_show_rand;   //显示二维码的大小
	int qr_show_y;    //显示二维码的Y位置
	int qr_show_xoset;   //二维码边缘位置
	t_lun_bo = 60 + pavo_sys.into_lun_bo;      //进入轮播倒计时

	pcc->scr_update = 0;
	S_text.Change_flg = 0;

	if ( pavo_sys.style == 2 ) {
		qr_show_rand = 250;   //显示二维码的大小
		qr_show_y = 160;    //显示二维码的Y位置
		qr_show_xoset = 35;   //二维码边缘位置
	}

	if ( pavo_sys.style == 3 ) {
		qr_show_rand = 250;   //显示二维码的大小
		qr_show_y = 160;    //显示二维码的Y位置
		qr_show_xoset = 35;   //二维码边缘位置
	}

	if ( pavo_sys.style == 1 ) {
		qr_show_rand = 250;   //显示二维码的大小
		qr_show_y = 115;    //显示二维码的Y位置
		qr_show_xoset = 35;   //二维码边缘位置
	}
	S_text.Top_line_roll_flg = pavo_clock.display_flg = flashing.flag = S_text.Cursor_flg = 0;
	//		pcc.t_int_to_lun_bo = set_qr_back_time + pavo_sys.into_lun_bo;	   //设定进入轮播的时间
	//	  pcc.t_pic_switch=pavo_sys.lun_time;
	//正文显示二维码
	if ( e->value1 == 0 ) {
		if ( pavo_sys.style == 1 ) t_well_come = 200 / tick;
		if ( pavo_sys.style == 2 ) t_well_come = 60000 / tick;
		switch ( e->value2 )
		{
			case 1 :  //靠左
				show_qrcode_text(scr_main, qr_show_xoset, qr_show_y, qr_show_rand, e->mem);
				break;
			case 2 :
				show_qrcode_text(scr_main, (LCD_XSIZE_TFT - qr_show_rand) / 2, qr_show_y, qr_show_rand, e->mem);
				break;
			case 3 :
				show_qrcode_text(scr_main, LCD_XSIZE_TFT - qr_show_rand - qr_show_xoset, qr_show_y, qr_show_rand, e->mem);
				break;
			default :
				show_qrcode_text(scr_main, (LCD_XSIZE_TFT - qr_show_rand) / 2, qr_show_y, qr_show_rand, e->mem);
				break;
		}
		scr_to_show(scr_main);
	}
	//先显示图片再显示二维码
	if ( e->value1 == 1 ) {

		t_well_come = 60000 / tick;
		Write_Byte_UART(e->value3);
		switch_paint_scr(scr_pic);
		if ( e->value3 < Internal_picture_max + 1 && e->value3 > 0 ) {
			switch_paint_scr(scr_pic);
			PAVO_Show_picture(pic_lop[e->value3 - 1], 0, 0, LCD_XSIZE_TFT, LCD_YSIZE_TFT);
		}

		switch ( e->value2 )
		{
			case 1 :  //靠左
				show_qrcode_text(scr_pic, qr_show_xoset, qr_show_y, qr_show_rand, e->mem);
				break;
			case 2 :
				show_qrcode_text(scr_pic, (LCD_XSIZE_TFT - qr_show_rand) / 2, qr_show_y, qr_show_rand, e->mem);
				break;
			case 3 :
				show_qrcode_text(scr_pic, LCD_XSIZE_TFT - qr_show_rand - qr_show_xoset, qr_show_y, qr_show_rand, e->mem);
				break;
			default :
				show_qrcode_text(scr_pic, (LCD_XSIZE_TFT - qr_show_rand) / 2, qr_show_y, qr_show_rand, e->mem);
				break;
		}

		scr_to_show(scr_pic);

	}
	if ( e->mem ) free(e->mem);
*/
}


void display_clock(void)
{
/*
	static u8 sec = 0;
	byte temp_x;
	if ( pavo_clock.seconds != sec ) {
		sec = pavo_clock.seconds;
		for ( temp_x = 0; temp_x < 20 ; temp_x++ )
			S_text.Textbuf[1][temp_x] = ' ';
		S_text.Textbuf[1][19] = pavo_clock.seconds % 10 + '0';
		S_text.Textbuf[1][18] = pavo_clock.seconds / 10 + '0';
		S_text.Textbuf[1][17] = ':';
		S_text.Textbuf[1][16] = pavo_clock.minutes % 10 + '0';
		S_text.Textbuf[1][15] = pavo_clock.minutes / 10 + '0';
		S_text.Textbuf[1][14] = ':';
		S_text.Textbuf[1][13] = pavo_clock.hours % 10 + '0';
		S_text.Textbuf[1][12] = pavo_clock.hours / 10 + '0';
		S_text.Change_flg = 1;
	}
*/
}


void event_timer_sec(void)
{
/*
	if ( time_sec < 1000 / tick ) return;
	if ( pavo_sys.style == 1 ) {
		if ( t_guest < 0XFF ) t_guest++;
		if ( t_guest == 60 && !pavo_clock.display_flg ) {
			Init_top_show();
			if ( !pavo_sys.into_lun_bo || (t_lun_bo && pavo_sys.into_lun_bo) ) S_text.Change_flg = 1;
		}
	}

	time_sec = 0;
	//每一秒钟执行一次
	if ( !pavo_sys.into_lun_bo || pavo_sys.pic_reg == 0 ) return;

	//产生图片轮播事件
	if ( !(S_text.Top_line_roll_flg || pavo_clock.display_flg || flashing.flag || S_text.Cursor_flg) ) {
		//光标状态,时钟状态,闪动等客显，均不全屏轮播
		if ( t_lun_bo ) {
			t_lun_bo--;
			t_pic_switch = 0;
		} else {
			if ( t_pic_switch ) {
				t_pic_switch--;
			} else {
				t_pic_switch = pavo_sys.lun_time;
				event_send(e_pavo, call_full_play, 0, 0, 0, 0);
			}
		}
	}
*/

}


void activate_system(void)
{
/*
	FIL fp;
	if ( FR_OK == f_open(&fp, "pavo.key", FA_CREATE_ALWAYS | FA_WRITE) ) {
		AES_Init(aes_key1);
		char *abuf = malloc(32);
		UINT aw;
		AES_Encrypt(chip_id, abuf, 32, (unsigned char *) aes_iv);
		f_write(&fp, abuf, 32, &aw);
		f_close(&fp);
		free(abuf);
	}
*/
}



bool check_Public_key_activate(void)
{
	bool ret = false;
/*
	FIL fp;
	if ( FR_OK == f_open(&fp, "pavo_public.key", FA_READ) ) {
		char *abuf = malloc(32);
		UINT aw;
		f_read(&fp, abuf, 32, &aw);
		if(abuf[0]=='P' && abuf[1]=='A' && abuf[2]=='V' && abuf[3]=='O' ){
			ret = true;
		}
		f_close(&fp);
		free(abuf);
	}
	if(ret ==true) {
		activate_system();
		f_unlink("pavo_public.key");
	}
*/
	return ret;
}




