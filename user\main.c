#include "rtos-freertos.h"
#include "lvgl.h"
#include "run_led.h"
#include "app_main.h"
#include "tlsf.h"
#include "Usart3.h"
#include "Serial.h"
#include "diskio.h"
#include "Keyboard.h"
#include "wheap.h"
#include "bsp_iwdg/bsp_iwdg.h" 
#include "bsp_systick/bsp_SysTick.h"


#include <stdio.h>
#include <stdlib.h>
#include <string.h>



static void MainTask_Handler(void* param);
TaskHandle_t MainTaskHandle;
unsigned char my_wheap_buf[1024*1024*6]   __attribute__((section(".bss.ARM.__at_0x60600000")));



extern lfs_t lfs;							
extern bool TestLITTLEFS(lfs_t *lfs);		// 文件系统测试函数


int main(void)
{
	// 上电延时
	for(unsigned long i = 0; i < 500000; i++){}
		
	BaseType_t ret;
	RCC_ClocksTypeDef RCC_Clocks;

	// 串口3中断优先级配置
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_4);

	// USART1初始化，用来打印输出调试
	BSP_USART_Configuration(115200);		
		
	log_level = LOG_LEVEL_INFO;
	printf("FreeRTOS Demo.\n");
	RCC_GetClocksFreq(&RCC_Clocks);
	printf("SYSCLK = %d.\n", RCC_Clocks.SYSCLK_Frequency);
	printf("AHBCLK = %d.\n", RCC_Clocks.HCLK_Frequency);
	printf("PCLK1  = %d.\n", RCC_Clocks.PCLK1_Frequency);
	printf("PCLK2  = %d.\n", RCC_Clocks.PCLK2_Frequency);

	// SDRAM配置
	SDRAM_PinConfig();
	SDRAM_Config();
	printf("SDRAM Init OK\n");
	
	// 内存池初始化
	Init_wheap(my_wheap_buf, sizeof(my_wheap_buf));

	// 文件系统初始化
	disk_initialize();
	
	// 创建主任务
	ret = xTaskCreate(Main_Task, "Main Task", 4*1024, NULL, 9, &MainTaskHandle);
	if (ret != pdPASS) {
		printf("Main Task Create Error.\n");
		while (1) {}
	}
	
	vTaskStartScheduler();

	while (1) 
	{
	
	}

	return 0;
}



