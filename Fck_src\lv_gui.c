#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "lv_gui.h"
#include "lv_demos.h"
#include "wheap.h"
#include "mh2457.h"
#include "app_main.h"
#include "Serial.h"
#include "wen_lib.h"
#include "checksum.h"
#include "PC_COM.h"
#include "track_fun_AES.h"
#include "HW.h"
#include "Keyboard.h"
#include "Mycommon.h"
#include "bsp_usart/bsp_usart.h"
#include "diskio.h"
#include "jdev.h"

#include "font_example.h"

extern SCREEN_TEXT S_text;
extern SYSTEM pavo_sys;
extern SYSTEM_ASC system_asc;
extern FLASHING flashing;
extern CLOCK pavo_clock;

extern void call_full_pic_one(WEN_event *e);   //	e_full_pic_one   	// 播放指定的图片
extern void call_rolly_wellcome(WEN_event *e); //	e_rolly_wellcome 	// 进行一次像素滚动,欢迎语
extern void call_key_event(WEN_event *e);	   //	e_key_trig
extern void call_updata_text(WEN_event *e);	   //	e_update_text
extern void call_show_qr(WEN_event *e);		   //	e_qrcode   			// 二维码播放事件
extern void call_show_qr_pix(WEN_event *e);	   //	e_qrcode_pix   		// 二维码播放事件

// extern render_t* render;
extern Event_list *e_pavo;

unsigned int Top_line_rolly_string[60]; // 顶行滚动缓存区
byte VFDONOFFcon;						// VFD 屏亮灭标志
byte DogWatch_flg = 1;
byte Top_line_rolly_tot; // 顶行滚动的字符数
byte demo_flag;
byte Iden_flg;
word Iden_number;
byte SecoudLineNumber; // 第二行滚动字符的字符数量
byte Rolling_Screen_flg, demo_number, CursorMode3LineNo;
byte COM_CTS_flg = 0; // 串口忙标志清0，表示不忙
byte COM_CTS_num = 0;
unsigned int TIME0_number;
extern FLASHING flashing;
word Time_number, demo_time_no;
PC_COM_FLG *pcc = NULL; // 存放临时变量
char file_path[256];
u32 get_file_lenth;
word StartDall;
byte StartDall_flg;
u8 pic_show;

P_Font *main_font = 0;
P_Font *mini_x_font = 0;
P_Font *system_font = 0;
P_Font *GBK_font = 0;

SCR *cur_paint_scr = 0;	  // 当前操作的界面
SCR *scr_main = 0;		  // 界面 main
SCR *scr_pic = 0;		  // 界面 pic
SCR *cur_show_page = 0;	  // 指示当前正在显示的页面
rt_Text *rt_wellcome = 0; // 欢迎语滚动器

unsigned char wel_message1[20] = "*** LCD DISPLAY  ***";
unsigned char wel_message2[40] = "**HAVE A NICE DAY AND THANK YOU **      ";
unsigned char Year_d[] = {0x0e, 0x0d, 0x0c, 0x0b, 0x0a, 0x09, 0x11, 0x10, 0x0f, 0x0e};
unsigned char Month_d[] = {0x0f, 0x0e, 0x0d, 0x0c, 0x0b, 0x0a, 0x12, 0x11, 0x10, 0x0f};
// CD5220的字符集顺序对应ESC/POS的字符集，保证字符集的序号一致
unsigned char BZZK_NO[14] = {'A', 'F', 'G', 'U', 'D', 'W', 'I', 'S', 'J', 'N', 'E', 'L', 'R'};

extern char *product_name;
extern char *mode[];

//====================================================LVGL显示============================================================//
extern void dis_px2(int x, int y, uint16_t c);

// 检查LVGL的内存配置是否满足演示程序的最低要求(38KB),如果不满足,编译器报错
#if LV_MEM_CUSTOM == 0 && LV_MEM_SIZE < (38ul * 1024ul)
#error Insufficient memory for lv_demo_widgets. Please set LV_MEM_SIZE to at least 38KB (38ul * 1024ul).  48KB is recommended.
#endif

#define LV_USE_CANVAS 1

#define CANVAS_WIDTH 960
#define CANVAS_HEIGHT 360
#define SCROLL_H 70

lv_obj_t *g_canvas = NULL; // 主画布
lv_color_t *cbuf = NULL;

lv_obj_t *down_canvas = NULL; // 底部滚动画布
lv_color_t *cbuf_down = NULL;

// GUI初始化
void lv_gui_init(void)
{

	/*********************************分配主画布************************************/
	// 内存分配
	cbuf = (lv_color_t *)wheapMalloc(CANVAS_WIDTH * CANVAS_HEIGHT * 2);
	if (!cbuf)
	{
		printf("Memory Allocate Error! \r\n");
		return; // 内存分配失败处理
	}

	// 创建画布
	g_canvas = lv_canvas_create(lv_scr_act());
	lv_canvas_set_buffer(g_canvas, cbuf, CANVAS_WIDTH, CANVAS_HEIGHT, LV_IMG_CF_TRUE_COLOR);

	// 白色画布背景
	lv_canvas_fill_bg(g_canvas, lv_color_make(0xFF, 0xFF, 0xFF), LV_OPA_COVER);
	lv_obj_set_pos(g_canvas, 0, 0);
}

void lv_scroll_canvas(void)
{
	/*********************************分配底部滚动画布************************************/
	cbuf_down = (lv_color_t *)wheapMalloc(CANVAS_WIDTH * SCROLL_H * 2);
	if (!cbuf_down)
	{
		printf("cbuf_down Memory Allocate Error! \r\n");
		return; // 内存分配失败处理
	}

	down_canvas = lv_canvas_create(lv_scr_act());

	lv_canvas_set_buffer(down_canvas, cbuf_down, CANVAS_WIDTH, SCROLL_H, LV_IMG_CF_TRUE_COLOR);
	lv_canvas_fill_bg(down_canvas, lv_color_make(0xFF, 0xFF, 0x00), LV_OPA_COVER);
	lv_obj_set_pos(down_canvas, 0, CANVAS_HEIGHT - SCROLL_H);
}

//============================================================================================================================//

//====================================================5寸客显pavo_gui.c程序移植====================================================//

void del_pfont(P_Font *f)
{
	wheapFree(f->font_data_buff);
	wheapFree(f);
}

P_Font *Init_pfont(char *file_name, u8 font_wide, u8 font_height)
{
	P_Font *xret;
	xret = (P_Font *)wheapMalloc(sizeof(P_Font));
	u16 crc = 0;

	if (xret)
	{
		xret->foreColor = mRGB(0, 0, 0); // 默认白底黑子
		xret->backColor = mRGB(255, 255, 255);
		xret->font_wide = font_wide;
		xret->font_height = font_height;
		xret->transparent = 0;

		if (xret)
		{
			lfs_file_t file;
			unsigned int read_coute;
			if (lfs_file_open(&lfs, &file, file_name, LFS_O_RDONLY) < 0)
			{
				printf("load font <%s> Fail .@file open !\r\n", file_name);
				xret->font_data_buff = wheapMalloc(font_wide * font_height * 255);
				for (long x = 0; x < (font_wide * font_height * 255) / 8; x++)
				{
					xret->font_data_buff[x] = 0;
				}
			}
			else
			{
				xret->font_data_buff = wheapMalloc(lfs_file_size(&lfs, &file) + 100);
				read_coute = lfs_file_read(&lfs, &file, xret->font_data_buff, lfs_file_size(&lfs, &file));
				crc = crc_ccitt_ffff((unsigned char *)xret->font_data_buff, read_coute);
				printf("font load size = %12d - crc = %X\r\n", read_coute, crc);
				lfs_file_close(&lfs, &file);
			}
		}
	}
	return xret;
}

// 初始化一个字幕滚动
void Init_rolly_text(rt_Text *rt, P_Font *pf, char *text)
{
	u8 bkg = 6; // 补6个空格
	char *char_dat;
	char_dat = text;
	rt->rolly_wide = 0;
	while (*char_dat++)
		rt->rolly_wide += pf->font_wide;
	for (int iu = 0; iu < bkg; iu++)
		rt->rolly_wide += pf->font_wide;
	rt->rolly_hight = pf->font_height;
	rt->pix_buf = (u16 *)wheapMalloc(rt->rolly_wide * rt->rolly_hight * 2);
	rt->rolly_coute = 0;

	int x, y, dx, xp;
	dx = 0;
	while (*text || bkg)
	{
		if (*text)
			char_dat = &pf->font_data_buff[(*text) * ((pf->font_wide * pf->font_height) / 8)]; // 获取到点阵指针
		else
			char_dat = &pf->font_data_buff[(' ') * ((pf->font_wide * pf->font_height) / 8)]; // 补空格

		// 复制字体到滚动缓冲
		x = 0;
		y = 0;
		xp = 0;
		while (xp < pf->font_wide * pf->font_height)
		{
			char xoh = 0X80;
			//	*char_dat =0xf0;
			while (xoh)
			{
				if ((*char_dat) & xoh)
					rt->pix_buf[dx + x + y * rt->rolly_wide] = pf->foreColor;
				else
					rt->pix_buf[dx + x + y * rt->rolly_wide] = pf->backColor;
				xoh >>= 1;
				if (++x >= pf->font_wide)
				{
					x = 0;
					y++;
				}
			}
			char_dat++;
			xp += 8;
		}
		// 滚动补5个空格
		if (*text == 0)
			bkg--;
		else
			text++;
		dx += pf->font_wide;
	}
}

// 删除字幕滚动
void del_rolly(rt_Text *s)
{
	wheapFree(s->pix_buf);
	wheapFree(s);
}

/**
 * @brief 	   创建新的滚动文本对象
 * @param      int bx, int by, int b_wide, P_Font *pf, char *text
 * @retval     rt_Text
 */
rt_Text *new_rolly_bind(int bx, int by, int b_wide, P_Font *pf, char *text)
{
	u8 bkg = 6; // 补6个空格
	char *char_dat;
	int x, y, dx, xp;

	// 初始化滚动文本结构体
	rt_Text *t_new = (rt_Text *)wheapMalloc(sizeof(rt_Text));
	t_new->canvas = g_canvas;
	t_new->bx = bx;
	t_new->by = by;
	t_new->b_wide = b_wide;

	// 计算滚动文本的宽度
	char_dat = text;
	t_new->rolly_wide = 0;
	while (*char_dat++)
		t_new->rolly_wide += pf->font_wide;
	for (int iu = 0; iu < bkg; iu++)
		t_new->rolly_wide += pf->font_wide; // 补6个空格

	// 计算滚动文本的高度(也即字体高度)
	t_new->rolly_hight = pf->font_height;

	t_new->pix_buf = (u16 *)wheapMalloc((200 + t_new->rolly_wide) * t_new->rolly_hight * 2);
	t_new->rolly_coute = 0; // 滚动计数器清零

	dx = 0;
	while (*text || bkg)
	{
		if (*text)
			char_dat = &pf->font_data_buff[(*text) * ((pf->font_wide * pf->font_height) / 8)]; // 获取到点阵指针
		else
			char_dat = &pf->font_data_buff[(' ') * ((pf->font_wide * pf->font_height) / 8)]; // 补空格

		// 复制字体到滚动缓冲
		x = 0; // x坐标
		y = 0; // y坐标
		xp = 0;
		while (xp < pf->font_wide * pf->font_height) // 处理一个字符的点阵数据
		{
			char xoh = 0X80; // 用于逐位处理
			while (xoh)
			{
				if ((*char_dat) & xoh)
					t_new->pix_buf[dx + x + y * t_new->rolly_wide] = pf->foreColor; // 绘制前景色
				else
					t_new->pix_buf[dx + x + y * t_new->rolly_wide] = pf->backColor; // 绘制背景色
				xoh >>= 1;
				if (++x >= pf->font_wide)
				{
					x = 0;
					y++;
				}
			}
			char_dat++; // 处理下一个字节
			xp += 8;
		}

		if (*text == 0) // 滚动补空格
			bkg--;
		else
			text++;

		dx += pf->font_wide; // 移动绘制位置到下一个字符区域
	}

	return t_new;
}

rt_Text *new_rolly_bind2(SCR *scr, int bx, int by, int b_wide, P_Font *pf, u16 *text)
{
	/*
		u8 qh, ql;
		u32 foffset;
		u8 bkg = 6; // 补6个空格
		char *char_dat;
		u16 *a_text;
		int x, y, dx, xp;
		rt_Text *t_new = (rt_Text *)wheapMalloc(sizeof(rt_Text));
		t_new->bind_scr = scr;
		t_new->bx = bx;
		t_new->by = by;
		t_new->b_wide = b_wide;

		//=====================================================

		a_text = text;
		t_new->rolly_wide = 0;
		// 计算字体长度
		while (*a_text++)
			t_new->rolly_wide += pf->font_wide;

		for (int iu = 0; iu < bkg; iu++)
			t_new->rolly_wide += pf->font_wide;
		// 补空格
		t_new->rolly_hight = pf->font_height;
		t_new->pix_buf = (u16 *)wheapMalloc(t_new->rolly_wide * t_new->rolly_hight * 2);
		t_new->rolly_coute = 0;

		dx = 0;
		while (*text || bkg)
		{
			if (*text < 0X80)
			{
				if (*text)
					char_dat = &main_font->font_data_buff[(*text) * ((main_font->font_wide * main_font->font_height) / 8)]; // 获取到点阵指针
				else
					char_dat = &main_font->font_data_buff[(' ') * ((main_font->font_wide * main_font->font_height) / 8)]; // 补空格
				pf = main_font;
			}
			else
			{
				// 显示双字节
				qh = (u8)(*text >> 8);
				ql = (u8)(*text);
				// 如果使用BIG5，那么进行BIG5转GKB ,然后调用GBK 字库
				if (pavo_sys.Expand_Char_No == 0x92)
				{
					if (big_to_gbk_table)
					{
						foffset = (qh - 0x81) * 191 + ql - 0x40;
						ql = (u8)(big_to_gbk_table[foffset] >> 8);
						qh = (u8)(big_to_gbk_table[foffset]);
					}
				}
				qh -= 0x81;
				ql -= 0x40;
				foffset = ((unsigned long)191 * qh + ql);
				if (*text)
					char_dat = &GBK_font->font_data_buff[foffset * ((GBK_font->font_wide * GBK_font->font_height) / 8)]; // 获取到点阵指针
				pf = GBK_font;
			}

			// 复制字体到滚动缓冲
			x = 0;
			y = 0;
			xp = 0;
			while (xp < pf->font_wide * pf->font_height)
			{
				char xoh = 0X80;
				//	*char_dat =0xf0;
				while (xoh)
				{
					if ((*char_dat) & xoh)
						t_new->pix_buf[dx + x + y * t_new->rolly_wide] = pf->foreColor;
					else
						t_new->pix_buf[dx + x + y * t_new->rolly_wide] = pf->backColor;
					xoh >>= 1;
					if (++x >= pf->font_wide)
					{
						x = 0;
						y++;
					}
				}
				char_dat++;
				xp += 8;
			}

			// 滚动补5个空格
			if (*text == 0)
			{
				bkg--;
			}
			else
				text++;

			dx += pf->font_wide;
		}
		return t_new;
	*/
	return NULL;
}

// 将一个字幕滚动绑定到一个屏幕
void rolly_bind(SCR *scr, rt_Text *rt, int bx, int by, int b_wide)
{
	/*
		rt->bind_scr = scr;
		rt->bx = bx;
		rt->by = by;
		rt->b_wide = b_wide;
	*/
}

static void rolly_timer_cb(lv_timer_t *timer)
{
	rt_Text *rt = (rt_Text *)timer->user_data;

	if (!is_empty())
	{
		return;
	}
	else
	{
		do_rolly_slice(rt);
	}
}

void create_rolly_timer(void)
{
	lv_timer_create(rolly_timer_cb, 5, rt_wellcome);
}

void do_rolly_slice(rt_Text *rt)
{
	const int LINES_PER_SLICE = 6; // 每次处理2行，可以调整
	int dx;

	if (!rt->is_processing)
	{
		rt->process_line = 0;
		rt->is_processing = 1;
	}

	// 处理当前片段的行
	int end_line = rt->process_line + LINES_PER_SLICE;
	if (end_line > rt->rolly_hight)
	{
		end_line = rt->rolly_hight;
	}

	for (int y = rt->process_line; y < end_line; y++)
	{
		for (int x = 0; x < rt->b_wide; x++)
		{
			dx = x + rt->rolly_coute;
			dx = dx % rt->rolly_wide;

			u16 color = rt->pix_buf[dx + y * rt->rolly_wide];
			lv_color_t lv_color = LV_COLOR_MAKE(
				(color >> 11) << 3,
				((color >> 5) & 0x3F) << 2,
				(color & 0x1F) << 3);
			lv_canvas_set_px(rt->canvas, rt->bx + x, rt->by + y, lv_color);
		}
	}

	rt->process_line = end_line;

	// 如果处理完了所有行
	if (rt->process_line >= rt->rolly_hight)
	{
		rt->is_processing = 0;
		// 更新滚动计数器
		rt->rolly_coute += 10;
		if (rt->rolly_wide <= rt->rolly_coute)
		{
			rt->rolly_coute = 0;
		}
	}
}

// 执行一次滚动
void do_rolly(rt_Text *rt)
{
	int dx;
	for (int y = 0; y < rt->rolly_hight; y++)
	{
		for (int x = 0; x < rt->b_wide; x++)
		{
			dx = x + rt->rolly_coute;
			dx = dx % rt->rolly_wide;

			// 获取缓冲区的颜色值(RGB565格式)
			u16 color = rt->pix_buf[dx + y * rt->rolly_wide];

			// 转换为LVGL的颜色格式
			lv_color_t lv_color = LV_COLOR_MAKE(
				(color >> 11) << 3,			// R
				((color >> 5) & 0x3F) << 2, // G
				(color & 0x1F) << 3			// B
			);

			lv_canvas_set_px(rt->canvas, rt->bx + x, rt->by + y, lv_color);
		}
	}

	// 更新滚动计数器
	// rt->rolly_coute++;
	rt->rolly_coute += 20;
	if (rt->rolly_wide <= rt->rolly_coute)
	{
		rt->rolly_coute = 0;
	}
}

void eq_cat(const char *str1, const char *str2, char *buff_temp)
{
	int x = 0;

	while (x < 10)
	{
		if (*str1 != 0)
		{
			buff_temp[x] = *str1++;
		}
		else
		{
			buff_temp[x] = ' ';
		}
		x++;
	}
	buff_temp[x++] = ':';
	buff_temp[x++] = ' ';
	while (x < 22)
	{
		if (*str2 != 0)
		{
			buff_temp[x] = *str2++;
		}
		else
		{
			buff_temp[x] = ' ';
		}
		x++;
	}
	buff_temp[x] = 0;
}

void Display_system(void)
{

	char buff_temp[64];
	const char *cmad = " Command ";
	const char *area_s = " area ";
	const char *s_char = " charset ";
	int x_start = 240;
	int y_satrt = 40;

	// switch_paint_scr(scr_main);
	pavo_fill_area(0, 0, LCD_XSIZE_TFT, LCD_YSIZE_TFT, mRGB(0, 0, 0));
	M_font_color_set(system_font, mRGB(255, 255, 255), mRGB(0, 0, 0));

	// 选择命令集
	switch (pavo_sys.Comm_No)
	{
	case 0:
		eq_cat(cmad, "LD220", buff_temp);
		break;
	case 1:
		eq_cat(cmad, "EPSON E.P", buff_temp);
		break;
	case 2:
		eq_cat(cmad, "AEDEX", buff_temp);
		break;
	case 3:
		eq_cat(cmad, "UTC/S", buff_temp);
		break;
	case 4:
		eq_cat(cmad, "UTC/E", buff_temp);
		break;
	case 5:
		eq_cat(cmad, "ADM788", buff_temp);
		break;
	case 6:
		eq_cat(cmad, "DSP800", buff_temp);
		break;
	case 7:
		eq_cat(cmad, "CD5220", buff_temp);
		break;
	case 8:
		eq_cat(cmad, "EMAX", buff_temp);
		break;
	case 9:
		eq_cat(cmad, "LOGIC-CON", buff_temp);
		break;
	case 0X0A:
		eq_cat(cmad, "ICD2002", buff_temp);
		break;
	case 0X0B:
		eq_cat(cmad, "EZ-202", buff_temp);
		break;
	case 0X0C:
		eq_cat(cmad, "PL-200", buff_temp);
		break;
	case 0X0D:
		eq_cat(cmad, "PD3000", buff_temp);
		break;
	default:
		eq_cat(cmad, "AUTO-ID", buff_temp);
		break;
	}

	M_Show_String(system_font, x_start, y_satrt, buff_temp);
	y_satrt += system_font->font_height;

	// 选择国家
	switch (pavo_sys.Char_No)
	{
	case 0:
		eq_cat(area_s, "U.S.A.", buff_temp);
		break;
	case 1:
		eq_cat(area_s, "France", buff_temp);
		break;
	case 2:
		eq_cat(area_s, "Germany", buff_temp);
		break;
	case 3:
		eq_cat(area_s, "U.K.", buff_temp);
		break;
	case 4:
		eq_cat(area_s, "Denmark I", buff_temp);
		break;
	case 5:
		eq_cat(area_s, "Sweden", buff_temp);
		break;
	case 6:
		eq_cat(area_s, "Italy", buff_temp);
		break;
	case 7:
		eq_cat(area_s, "Spain", buff_temp);
		break;
	case 8:
		eq_cat(area_s, "Japan", buff_temp);
		break;
	case 9:
		eq_cat(area_s, "Norway", buff_temp);
		break;
	case 0XA:
		eq_cat(area_s, "Denmark II", buff_temp);
		break;
	case 0XB:
		eq_cat(area_s, "Slavonic", buff_temp);
		break;
	case 0XC:
		eq_cat(area_s, "Russia", buff_temp);
		break;
	case 0XD:
		eq_cat(area_s, "Greek", buff_temp);
		break;
	case 0XE:
		eq_cat(area_s, "Czech", buff_temp);
		break;
	case 0XF:
		eq_cat(area_s, "Lithuania", buff_temp);
		break;
	case 0X10:
		eq_cat(area_s, "Portugal", buff_temp);
		break;
	case 0X11:
		eq_cat(area_s, "Turkey", buff_temp);
		break;
	case 0X12:
		eq_cat(area_s, "Baltic States", buff_temp);
		break;
	case 0X13:
		eq_cat(area_s, "Thailand", buff_temp);
		break;
	default:
		eq_cat(area_s, "U.K.", buff_temp);
		break;
	}

	M_Show_String(system_font, x_start, y_satrt, buff_temp);
	y_satrt += system_font->font_height;

	eq_cat(s_char, pcc->cur_font_name, buff_temp);
	M_Show_String(system_font, x_start, y_satrt, buff_temp);
	y_satrt += system_font->font_height;

	// 显示模式
	if (pavo_sys.line_mode == 1)
	{
		eq_cat(" show type ", "20 X 2  ", buff_temp);
	}
	else
	{
		eq_cat(" show type ", "20 X 4  ", buff_temp);
	}

	M_Show_String(system_font, x_start, y_satrt, buff_temp);
	y_satrt += system_font->font_height;

	// 波特率
	switch (pavo_sys.Baud_Rate)
	{
	case 0:
		eq_cat(" Baud rate ", "9600", buff_temp);
		break;

	case 1:
		eq_cat(" Baud rate ", "19200", buff_temp);
		break;

	case 2:
		eq_cat(" Baud rate ", "38400", buff_temp);
		break;

	case 3:
		eq_cat(" Baud rate ", "57600", buff_temp);
		break;

	case 4:
		eq_cat(" Baud rate ", "115200", buff_temp);
		break;

	default:
		eq_cat(" Baud rate ", "9600", buff_temp);
		break;
	}

	M_Show_String(system_font, x_start, y_satrt, buff_temp);
	y_satrt += system_font->font_height;

	char c_buf[512];

	// 软件版本
	sprintf(c_buf, "%d", TFT_VERSION);
	eq_cat(" Firmware", c_buf, buff_temp);
	M_Show_String(system_font, x_start, y_satrt, buff_temp);
	y_satrt += system_font->font_height;

	// scr_to_show(scr_main); // 缓冲刷新入显示
}

// 显示一个字符
void M_Show_Char(P_Font *pf, int x, int y, int ch)
{
	int dx = x;
	char *char_dat = &pf->font_data_buff[ch * ((pf->font_wide * pf->font_height) / 8)];
	long xp = 0;
	while (xp < pf->font_wide * pf->font_height)
	{
		char xoh = 0X80;
		while (xoh)
		{
			// if ( (*char_dat) & xoh )
			// 	cur_paint_scr->pix_dat[x + y * LCD_XSIZE_TFT] = pf->foreColor;
			// else
			// 	cur_paint_scr->pix_dat[x + y * LCD_XSIZE_TFT] = pf->backColor;

			u16 color = ((*char_dat) & xoh) ? pf->foreColor : pf->backColor;
			lv_color_t lv_color = LV_COLOR_MAKE(
				(color >> 11) << 3,			// R 5bit转8bit (左移3位)
				((color >> 5) & 0x3F) << 2, // G 6bit转8bit (左移2位)
				(color & 0x1F) << 3			// B 5bit转8bit (左移3位)
			);
			lv_canvas_set_px(g_canvas, x, y, lv_color);

			xoh >>= 1;
			if (++x >= dx + pf->font_wide)
			{
				x = dx;
				y++;
			}
		}
		char_dat++;
		xp += 8;
	}
}

void M_font_color_set(P_Font *pf, u16 foreColor, u16 backColor)
{
	pf->foreColor = foreColor;
	pf->backColor = backColor;
}

void M_Show_String(P_Font *pf, int x, int y, char *string)
{
	while (x < LCD_XSIZE_TFT && *string)
	{
		M_Show_Char(pf, x, y, *string++);
		x += pf->font_wide;
	}
}

void scr_to_show(SCR *scr) // 将界面缓冲刷新到屏幕
{
	cur_show_page = scr;
	Show_Screen_area(scr->pix_dat, 0, 0, LCD_XSIZE_TFT, LCD_YSIZE_TFT);
}

char cursor_data[80 * 80] = {0XFF};
u16 Text_buff[2][20];
void update_Show_customer()
{
	int foffset;
	int start_x;
	u8 qh, ql;

	// 设置字体颜色
	M_font_color_set(main_font, pavo_sys.font_Foreground_color, pavo_sys.font_Background_color);

	// 传统模式
	if (pavo_sys.style == 1)
	{
		pavo_fill_area(0, 70, LCD_XSIZE_TFT, LCD_YSIZE_TFT - 140, main_font->backColor);
		printf("main_font->font_height = %d\r\n", main_font->font_height);
		if (pavo_sys.line_mode == 1) // 单行模式, 显示两行
		{
			for (int ly = 0; ly < 2; ly++)
			{
				for (int lx = 0; lx < 20; lx++)
				{
					// 显示中间两行
					M_Show_Char(main_font, lx * main_font->font_wide, 100 + ly * main_font->font_height, S_text.Textbuf[ly][lx]);
				}
			}
		}
		else // 多行模式, 显示四行
		{
			for (int ly = 0; ly < 4; ly++)
			{
				for (int lx = 0; lx < 20; lx++)
				{
					M_Show_Char(main_font, lx * main_font->font_wide, 70 + ly * main_font->font_height, S_text.Textbuf[ly][lx]);
				}
			}
		}
	}

	// 顶部中文模式
	if (pavo_sys.style == 2)
	{
		// 设置字体颜色
		M_font_color_set(GBK_font, main_font->foreColor, main_font->backColor);
		pavo_fill_area(0, 0, LCD_XSIZE_TFT, 120, main_font->backColor);
		for (int ly = 0; ly < 2; ly++)
		{
			start_x = 0;
			for (int lx = 0; lx < 20; lx++)
			{
				if (S_text.Textbuf[ly][lx] <= 0X80)
				{
					M_Show_Char(main_font, start_x, ly * (main_font->font_height + 12) + 11, S_text.Textbuf[ly][lx]);
				}
				else
				{
					// 显示双字节
					qh = (u8)(S_text.Textbuf[ly][lx] >> 8);
					ql = (u8)S_text.Textbuf[ly][lx];
					// 如果使用BIG5，那么进行BIG5转GKB ,然后调用GBK 字库
					if (pavo_sys.Expand_Char_No == 0X92)
					{
						if (big_to_gbk_table)
						{
							foffset = (qh - 0x81) * 191 + ql - 0x40;
							ql = (u8)(big_to_gbk_table[foffset] >> 8);
							qh = (u8)(big_to_gbk_table[foffset]);
						}
					}
					if (qh >= 0X81 && ql >= 0X40)
					{
						qh -= 0x81;
						ql -= 0x40;
						foffset = ((unsigned long)191 * qh + ql);
						M_Show_Char(GBK_font, start_x, ly * (GBK_font->font_height + 12) + 11, foffset);
					}
				}
				start_x += GBK_font->font_wide;
			}
		}
	}

	if (pavo_sys.style == 3)
	{
		M_font_color_set(GBK_font, main_font->foreColor, main_font->backColor);
		pavo_fill_area(0, 0, LCD_XSIZE_TFT, 160, main_font->backColor);
		for (int ly = 0; ly < 2; ly++)
		{
			start_x = 0;
			for (int lx = 0; lx < 20; lx++)
			{
				M_Show_Char(main_font, start_x, ly * (main_font->font_height), S_text.Textbuf[ly][lx]);
				start_x += main_font->font_wide;
			}
		}
	}

	// printf("pcc->cursor_rf = %d\r\n", pcc->cursor_rf);

	// 光标显示控制
	if (pcc->cursor_rf)
	{
		int l, w;
		w = (S_text.Cursor % 20) * main_font->font_wide;
		if (pavo_sys.style == 1)
		{
			l = (S_text.Cursor / 20) * main_font->font_height;
			if (pavo_sys.line_mode == 1)
				l += 160;
			if (pavo_sys.line_mode == 2)
				l += 80;
		}
		if (pavo_sys.style == 2)
		{
			l = (S_text.Cursor / 20) * (main_font->font_height + 12) + 11;
		}
		for (int j = l; j < l + main_font->font_height; j++)
		{
			for (int k = w; k < w + main_font->font_wide; k++)
			{
				// scr_main->pix_dat[k + j * LCD_XSIZE_TFT] = main_font->foreColor;

				lv_color_t cursor_color = LV_COLOR_MAKE(
					(main_font->foreColor >> 11) << 3,		   // R分量扩展
					((main_font->foreColor >> 5) & 0x3F) << 2, // G分量扩展
					(main_font->foreColor & 0x1F) << 3		   // B分量扩展
				);
				lv_canvas_set_px(g_canvas, k, j, cursor_color);
			}
		}
	}
}

//----------------------------------顶行滚动模块------------------------------------
void Top_line_rolly_display()
{
	byte temp_i;

	S_text.Roll_t = 0;

	flashing.flag = 0;

	if (!demo_time_no)
	{
		demo_time_no = set_demo_time / tick;
		for (temp_i = 0; temp_i < 19; temp_i++)
			S_text.Textbuf[0][temp_i] = S_text.Textbuf[0][temp_i + 1];

		if (0 == Top_line_rolly_string[demo_number])
			S_text.Textbuf[0][19] = ' ';
		else
			S_text.Textbuf[0][19] = Top_line_rolly_string[demo_number];
		demo_number++;
		if (demo_number >= Top_line_rolly_tot)
			demo_number = 0;
		for (temp_i = 0; temp_i < 20; temp_i++)
			S_text.Text_f_buf[0][temp_i] = 0;
		S_text.Change_flg = 1;
	}
}

char *get_part(char *out, char *in)
{
	*out = 0;
	// 跳过其他字符
	while (*in)
	{
		if (*in == '\0' || *in == '\n' || *in == '\r' || *in == ' ' || *in == ',')
		{
			in++;
		}
		else
			break;
	}
	// 参数
	while (*in)
	{
		*out++ = *in++;
		if (*in == '\0' || *in == '\n' || *in == '\r' || *in == ' ' || *in == ',')
		{
			*out = 0;
			break;
		}
	}
	return in;
}

// static long atol(char *str)
//{
//	char sd = 1;
//	long ret = 0;
//	while(*str) {
//		if ( *str == '-' ) sd = sd * (-1);
//		if ( *str >= '0' && *str <= '9' ) {
//			ret = ret * 10;
//			ret += *str - '0';
//		}
//		str++;
//	}
//	return ret * sd;
// }

static char *itoa_dec(long val, char *buf)
{
	char *p;
	char *firstdig;
	char temp;
	unsigned int digval;
	unsigned int radix = 10; // 转换成10进制
	p = buf;
	if (val < 0)
	{
		*p++ = '-';
		val = -val;
	}
	firstdig = p;
	do
	{
		digval = (unsigned)(val % radix);
		val /= radix;
		*p++ = (char)(digval + '0');
	} while (val > 0);

	*p-- = '\0';
	do
	{
		temp = *p;
		*p = *firstdig;
		*firstdig = temp;
		--p;
		++firstdig;
	} while (firstdig < p);
	return buf;
}

// 从串口中获取以‘\n’ 为结尾的字符串。
char uart_get_line(char *dat, int buf_len, int delay)
{
	*dat = 0;
	while (delay > 0 && buf_len)
	{
		if (is_empty())
		{
			delay_ms(tick);
			delay -= 2;
		}
		else
		{
			buf_len--;
			*dat = Read_Byte_UART();
			if (*dat == '\n' || *dat == 0)
			{
				*dat = 0;
				return 1;
			}
			dat++;
		}
	}
	return 0;
}

static bool wen_strcmp(char *s1, char *s2)
{
	while (*s1 && *s2 && *s1 != '\n' && *s2 != '\n')
	{
		if (*s1++ != *s2++)
			return false;
	}
	return true;
}

// 文件管理指令02 07 05

void file_ctrl()
{
	u8 temp = Read_Byte_UART();
	void *buff_work;
	u32 rcoute;
	uint32_t send_len;
	u16 crc, file_crc;
	char *data_buf;
	unsigned int fw;

	lfs_file_t file; // file Type

	int x;
	char dbuf[128];
	char out[128];
	char *pstr;

	// printf("file ctrl 0x%X\r\n", temp);

	switch (temp)
	{
	case 0: // 创建LittleFS
		if (0XAA == Read_Byte_UART())
		{
			if (0XAA == Read_Byte_UART())
			{
				if (0X55 == Read_Byte_UART())
				{
					if (0X55 == Read_Byte_UART())
					{
						if (lfs_format(&lfs, &lfs_cfg) == LFS_ERR_OK)
						{
							Write_Byte_UART('O');
							Write_Byte_UART('K');
							Write_Byte_UART('\n');
							Write_Byte_UART(0);
						}
						else
						{
							Write_Byte_UART('F');
							Write_Byte_UART('L');
							Write_Byte_UART('\n');
							Write_Byte_UART(0);
						}
					}
				}
			}
		}
		break;
		
	case 1: // 文件下载
		// 设置要读写的文件名以0为结束
		while (is_empty())
		{
		} // 等待指令输入

		for (x = 0; x < 256; x++)
		{
			dbuf[x] = Read_Byte_UART();
			if (dbuf[x] == '\n')
			{
				break;
			}
		}

		if (dbuf[x] == '\n')
		{
			pstr = dbuf;
			// 提取第一个参数
			pstr = get_part(file_path, pstr); // 获取文件名
			// 提取第二个参数
			pstr = get_part(out, pstr); // 获取文件长度
			get_file_lenth = atol(out); // 转换成长整型
			// 提取第三个参数
			pstr = get_part(out, pstr); // 获取文件CRC
			file_crc = atol(out);

			printf("receive <%s> size = %d  crc = %d\r\n", file_path, get_file_lenth, file_crc);

			data_buf = wheapMalloc(get_file_lenth + 1);
			if (data_buf == 0)
			{
				printf("error 内存\r\n");
				Write_Byte_UART('F');
				Write_Byte_UART('L');
				Write_Byte_UART('\n');
				break;
			}

			// 发送OK
			Write_Byte_UART('O');
			Write_Byte_UART('K');
			Write_Byte_UART('\n');

			crc = 0xFFFF;
			rcoute = 0;
			while (rcoute < get_file_lenth)
			{
				x = 0;
				while (x < 200 && is_empty())
				{
					delay_ms(10);
					x++;
				}

				if (x < 200)
				{
					data_buf[rcoute] = Read_Byte_UART();
					crc = update_crc_ccitt(crc, data_buf[rcoute]);
					rcoute++;
				}
				else
				{
					printf("time out. stop\r\n");
					break;
				}
			}

			if (rcoute < get_file_lenth)
			{
				// 数据接收不完整
				Write_Byte_UART('F');
				Write_Byte_UART('L');
				Write_Byte_UART('\n');
				wheapFree(data_buf);
				break;
			}

			// 上传 CRC
			printf("上传CRC\r\n");
			itoa_dec(crc, out); // CRC 转换成字符串
			pstr = out;
			while (*pstr)
			{
				Write_Byte_UART(*pstr++);
			}
			Write_Byte_UART('\n');

			printf("recv end !\r\n");

			// 校验 CRC
			if (file_crc == crc)
			{
				printf("crc check succeed\r\n");

				lfs_remove(&lfs, file_path);
				printf("Start open the file\r\n");
				if (lfs_file_open(&lfs, &file, "temp_flxxx5698.bin", LFS_O_WRONLY | LFS_O_CREAT | LFS_O_TRUNC) == LFS_ERR_OK)
				{
					lfs_remove(&lfs, file_path); // 删除旧文件

					if (rcoute != lfs_file_write(&lfs, &file, data_buf, rcoute))
					{
						printf("文件写入错误\r\n");
						Write_Byte_UART('F');
						Write_Byte_UART('L');
						Write_Byte_UART('\n');
					}
					else
					{
						lfs_file_close(&lfs, &file);
						lfs_rename(&lfs, "temp_flxxx5698.bin", file_path);
						printf("文件写入成功并重命名\r\n");

						if (true == wen_strcmp(file_path, "firmware.bin"))
						{
							DogWatch_flg = 0;
						}

						// 清空串口剩余数据
						while (!is_empty())
						{
							temp = Read_Byte_UART();
						}
					}
				}
				else
				{
					printf("error file open for write\r\n");
					Write_Byte_UART('F');
					Write_Byte_UART('L');
					Write_Byte_UART('\n');
				}
			}
			else
			{
				printf("crc check fail\r\n");
				Write_Byte_UART('F');
				Write_Byte_UART('L');
				Write_Byte_UART('\n');
			}

			wheapFree(data_buf);
		}
		printf("==============================\r\n");

		Init_scr();
		break;

	case 2: // 文件读取上传
		while (is_empty())
		{
		} // 等待指令输入
		for (x = 0; x < 256; x++)
		{
			dbuf[x] = Read_Byte_UART();
			if (dbuf[x] == '\n')
			{
				break;
			}
		}
		if (dbuf[x] == '\n')
		{
			// 提取第一个参数
			get_part(file_path, dbuf); // 解析文件名

			if (LFS_ERR_OK == lfs_file_open(&lfs, &file, file_path, LFS_O_RDONLY))
			{
				lfs_file_seek(&lfs, &file, 0, LFS_SEEK_END); // 定位到文件末尾
				get_file_lenth = lfs_file_tell(&lfs, &file); // 获取文件总大小
				itoa_dec(get_file_lenth, out);				 // 转换成字符串

				// 文件名
				pstr = file_path;
				while (*pstr)
				{
					Write_Byte_UART(*pstr++);
				}

				Write_Byte_UART(',');

				// 文件长度
				pstr = out;
				while (*pstr)
				{
					Write_Byte_UART(*pstr++);
				}

				Write_Byte_UART('\n');

				if (uart_get_line(dbuf, 128, 300))
				{
					if (dbuf[0] == 'O' && dbuf[1] == 'K')
					{
						// 收到回复开始传送文件
						crc = 0XFFFF;
						while (get_file_lenth)
						{
							// 分块读取，每次读取128字节
							if (get_file_lenth >= 128)
							{
								get_file_lenth -= 128;
								rcoute = 128;
							}
							else
							{
								rcoute = get_file_lenth;
								get_file_lenth = 0;
							}
							lfs_file_read(&lfs, &file, dbuf, rcoute); // 读取文件内容

							// 串口送出数据并统计CRC
							crc = update_crc_ccitt_buf(crc, (unsigned char *)dbuf, rcoute);
							for (x = 0; x < rcoute; x++)
							{
								Write_Byte_UART(dbuf[x]);
							}
						}

						// 发送最终CRC校验值
						itoa_dec(crc, dbuf);
						pstr = dbuf;
						while (*pstr)
						{
							Write_Byte_UART(*pstr++);
						}
						Write_Byte_UART('\n');
					}
					else
					{
						printf("error no OK\r\n");
					}
				}
				lfs_file_close(&lfs, &file);
			}
			else
			{
				Write_Byte_UART('F');
				Write_Byte_UART('L');
				Write_Byte_UART('\n');
			}
		}
		break;
	case 3:

		break;
	case 4:
		break;
	case 5: // 读取文件长度和CRC
		while (is_empty())
		{
		} // 等待指令输入
		for (x = 0; x < 256; x++)
		{
			dbuf[x] = Read_Byte_UART();
			if (dbuf[x] == '\n')
			{
				break;
			}
		}

		if (dbuf[x] == '\n')
		{
			pstr = dbuf;
			// 提取第一个参数,要校验的文件名
			pstr = get_part(file_path, pstr);

			send_len = 0;
			crc = 0XFFFF;
			printf("receive <%s> size = %d    crc = %d\r\n", file_path, get_file_lenth, file_crc);
			if (LFS_ERR_OK == lfs_file_open(&lfs, &file, file_path, LFS_O_RDONLY)) // 以只读模式打开文件
			{
				send_len = get_file_lenth = lfs_file_size(&lfs, &file); // 获取文件总大小
				// 开始计算CRC
				while (get_file_lenth)
				{
					if (get_file_lenth >= 128)
					{
						get_file_lenth -= 128;
						rcoute = 128;
					}
					else
					{
						rcoute = get_file_lenth;
						get_file_lenth = 0;
					}
					lfs_file_read(&lfs, &file, dbuf, rcoute);
					// 串口送出数据并统计CRC
					crc = update_crc_ccitt_buf(crc, (unsigned char *)dbuf, rcoute); // 更新CRC
				}
				lfs_file_close(&lfs, &file);
			}

			// 发送校验结果（格式：文件名,文件长度,文件CRC）
			// 往上发文件名
			pstr = file_path;
			while (*pstr)
				Write_Byte_UART(*pstr++);
			Write_Byte_UART(',');

			// 发文件长度
			itoa_dec(send_len, dbuf);
			pstr = dbuf;
			while (*pstr)
				Write_Byte_UART(*pstr++);
			Write_Byte_UART(',');

			// 发送文件CRC
			itoa_dec(crc, dbuf);
			pstr = dbuf;
			while (*pstr)
				Write_Byte_UART(*pstr++);
			Write_Byte_UART('\n');
		}
		break;
	}
}

static char *pavo_itoa(uint32_t val, char *buf, unsigned int radix)
{
	char *p;
	char *firstdig;
	char temp;
	unsigned int digval;
	p = buf;
	firstdig = p;
	do
	{
		digval = (unsigned)(val % radix);
		val /= radix;

		if (digval > 9)
			*p++ = (char)(digval - 10 + 'A');
		else
			*p++ = (char)(digval + '0');
	} while (val > 0);

	*p-- = '\0';
	do
	{
		temp = *p;
		*p = *firstdig;
		*firstdig = temp;
		--p;
		++firstdig;
	} while (firstdig < p);
	return buf;
}

static void inser_uint32(char *key_name, uint32_t dat, char end)
{
	char abuf[32];
	Write_Byte_UART('"');
	while (*key_name)
	{
		Write_Byte_UART(*key_name++);
	}
	Write_Byte_UART('"');
	Write_Byte_UART(':');
	pavo_itoa(dat, abuf, 10);
	for (int x = 0; x < 32 && abuf[x] != 0; x++)
	{
		Write_Byte_UART(abuf[x]);
	}
	if (!end)
	{
		Write_Byte_UART(',');
		Write_Byte_UART('\n');
	}
}

// 双字节字体
static void inser_string(char *key_name, u16 *key, char end)
{
	u8 ad;
	Write_Byte_UART('"');
	while (*key_name)
	{
		Write_Byte_UART(*key_name++);
	}
	Write_Byte_UART('"');
	Write_Byte_UART(':');
	Write_Byte_UART('"');
	int x = 0;
	while (key[x] != 0)
	{
		if (key[x] >= 0X8140)
		{
			ad = (u8)(key[x] >> 8);
			if (!ad)
				break;
			Write_Byte_UART(ad);
			ad = (u8)key[x];
			if (!ad)
				break;
			Write_Byte_UART(ad);
		}
		else
		{
			Write_Byte_UART((u8)key[x]);
		}
		x++;
	}
	Write_Byte_UART('"');
	if (!end)
	{
		Write_Byte_UART(',');
		Write_Byte_UART('\n');
	}
}

static void put_sysconf()
{
	// 上传开头
	Write_Byte_UART('{');
	Write_Byte_UART('\n');
	// 上传内容
	inser_uint32("temp1", pavo_sys.temp1, 0);
	inser_uint32("Baud_Rate", pavo_sys.Baud_Rate, 0);
	inser_uint32("temp2", pavo_sys.temp2, 0);
	inser_uint32("Char_No", pavo_sys.Char_No, 0);
	inser_uint32("Expand_Char_No", pavo_sys.Expand_Char_No, 0);
	inser_uint32("Comm_No", pavo_sys.Comm_No, 0);

	inser_string("wel_mess1", pavo_sys.wel_mess1, 0); // 最后一个字节为1标识这个是最后一个项
	inser_string("wel_mess2", pavo_sys.wel_mess2, 0); // 最后一个字节为1标识这个是最后一个项

	inser_uint32("font_Foreground_color", pavo_sys.font_Foreground_color, 0);
	inser_uint32("font_Background_color", pavo_sys.font_Background_color, 0);
	inser_uint32("font_pc_color", pavo_sys.font_pc_color, 0);
	inser_uint32("back_pc_color", pavo_sys.back_pc_color, 0);

	inser_uint32("back_light", pavo_sys.back_light, 0);
	inser_uint32("pic_reg", pavo_sys.pic_reg, 0);
	inser_uint32("line_mode", pavo_sys.line_mode, 0);

	inser_uint32("bm_back_rgb", mRGB(pavo_sys.bm_back_r, pavo_sys.bm_back_g, pavo_sys.bm_back_b), 0);
	inser_uint32("bm_font_rgb", mRGB(pavo_sys.bm_font_r, pavo_sys.bm_font_g, pavo_sys.bm_font_b), 0);

	inser_uint32("tp_back_rgb", mRGB(pavo_sys.tp_back_r, pavo_sys.tp_back_g, pavo_sys.tp_back_b), 0);
	inser_uint32("tp_font_rgb", mRGB(pavo_sys.tp_font_r, pavo_sys.tp_font_g, pavo_sys.tp_font_b), 0);

	inser_uint32("tp_mode", pavo_sys.tp_mode, 0);

	inser_string("main_str", pavo_sys.main_str, 0);

	inser_uint32("into_lun_bo", pavo_sys.into_lun_bo, 0);
	inser_uint32("lun_time", pavo_sys.lun_time, 0);
	inser_uint32("into_well_rolly", pavo_sys.into_well_rolly, 0);

	inser_uint32("mode_type", pavo_sys.mode_type, 0);
	inser_uint32("style", pavo_sys.style, 1);
	// 上传结尾
	Write_Byte_UART('\n');
	Write_Byte_UART('}');
	Write_Byte_UART(0);
}

extern bool flag;

// 02 07 控制指令
void sys_Ctrl_by_com(void)
{
	u8 dtemp[32];
	u8 sr, sg, sb;
	u32 wtemp;
	u8 temp;
	u8 x;
	u16 temp_char;
	int xint;
	char *temp_mem;
	unsigned char *aes_in = 0;
	unsigned char *aes_out = 0;
	char *str_temp;
	temp = Read_Byte_UART();

	// printf("sys_Ctrl_by_com 0x%X\r\n", temp);

	switch (temp)
	{
	case 0x0: // 旧VFD 命令，上下两行不滚动。	设置第一行和第二行是否滚动    十六进制  格式：[02H][07H] n	   01H，第二行滚动，02H，第一行滚动,00H 都不滚动
		pavo_sys.temp2 = temp;
		save_system_mesg();
		DogWatch_flg = 0;
		break;

	case 0x1:
		pavo_sys.temp2 = temp;
		save_system_mesg();
		DogWatch_flg = 0;
		break;

	case 0x2:
		pavo_sys.temp2 = temp;
		save_system_mesg();
		DogWatch_flg = 0;
		break;

	case 0x3:
		for (int x = 0; x < 13; x++)
		{
			if ((1 << x) & pavo_sys.pic_reg)
			{
				if (x < 0X0A)
				{
					Write_Byte_UART(x + '0');
					Write_Byte_UART(' ');
				}
				else
				{
					Write_Byte_UART('1');
					Write_Byte_UART(x - 0X0A + '0');
					Write_Byte_UART(' ');
				}
			}
		}
		Write_Byte_UART(0X0D);
		Write_Byte_UART(0X0A);
		break;

	case 0x4: // 临时更换波特率
		wtemp = Read_Byte_UART();
		wtemp <<= 8;
		wtemp |= Read_Byte_UART();
		wtemp <<= 8;
		wtemp |= Read_Byte_UART();
		wtemp <<= 8;
		wtemp |= Read_Byte_UART();

		HW_UART_init(wtemp); // 切换波特率

		Write_Byte_UART('\n');
		pcc->temp_baud = 1;
		break;

	case 0x5: // 文件管理指令
		file_ctrl();
		break;

	case 0x9: // 设置顶部显示图片	tp_mode=0X09  显示图片
		pavo_sys.tp_mode = 0X09;
		save_system_mesg();
		Init_scr();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X09);
		break;

	case 0xA: // 设置顶部显示文本	tp_mode=0X0A  显示文本
		pavo_sys.tp_mode = 0X0A;
		printf("Coming in the case 0x0A\r\n");
		save_system_mesg();
		printf("save_system_mesg Success\r\n");
		Init_scr();
		printf("Init_scr Success\r\n");
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X0A);
		break;

	case 0XB: // 设置顶部标题文字颜色
		pavo_sys.tp_font_r = Read_Byte_UART();
		pavo_sys.tp_font_g = Read_Byte_UART();
		pavo_sys.tp_font_b = Read_Byte_UART();
		save_system_mesg();
		Init_scr();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X0B);
		Write_Byte_UART(pavo_sys.tp_font_r);
		Write_Byte_UART(pavo_sys.tp_font_g);
		Write_Byte_UART(pavo_sys.tp_font_b);
		break;

	case 0XC: // 设置顶部标题标题颜色
		pavo_sys.tp_back_r = Read_Byte_UART();
		pavo_sys.tp_back_g = Read_Byte_UART();
		pavo_sys.tp_back_b = Read_Byte_UART();
		save_system_mesg();
		Init_scr();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X0C);
		Write_Byte_UART(pavo_sys.tp_back_r);
		Write_Byte_UART(pavo_sys.tp_back_g);
		Write_Byte_UART(pavo_sys.tp_back_b);
		break;

	case 0X0D: // 设置4行运行还是2行运行
		break;

	case 0X0E: // 设置正文文字颜色
		sr = Read_Byte_UART();
		sg = Read_Byte_UART();
		sb = Read_Byte_UART();
		pavo_sys.font_Foreground_color = mRGB(sr, sg, sb);
		pavo_sys.font_pc_color = pavo_sys.font_Foreground_color;
		Init_scr();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X0E);
		Write_Byte_UART(sr);
		Write_Byte_UART(sg);
		Write_Byte_UART(sb);
		break;

	case 0X0F: // 设置正文背景颜色
		sr = Read_Byte_UART();
		sg = Read_Byte_UART();
		sb = Read_Byte_UART();
		pavo_sys.font_Background_color = mRGB(sr, sg, sb);
		pavo_sys.back_pc_color = pavo_sys.font_Background_color;
		Init_scr();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X0F);
		Write_Byte_UART(sr);
		Write_Byte_UART(sg);
		Write_Byte_UART(sb);
		break;

	case 0x10: // 设置底部消息文字颜色
		pavo_sys.bm_font_r = Read_Byte_UART();
		pavo_sys.bm_font_g = Read_Byte_UART();
		pavo_sys.bm_font_b = Read_Byte_UART();
		save_system_mesg();
		Init_scr();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X10);
		Write_Byte_UART(pavo_sys.bm_font_r);
		Write_Byte_UART(pavo_sys.bm_font_g);
		Write_Byte_UART(pavo_sys.bm_font_b);
		break;

	case 0X11: // 设置底部消息背景颜色
		pavo_sys.bm_back_r = Read_Byte_UART();
		pavo_sys.bm_back_g = Read_Byte_UART();
		pavo_sys.bm_back_b = Read_Byte_UART();
		save_system_mesg();
		Init_scr();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X11);
		Write_Byte_UART(pavo_sys.bm_back_r);
		Write_Byte_UART(pavo_sys.bm_back_g);
		Write_Byte_UART(pavo_sys.bm_back_b);
		break;

	case 0X12:
		// 使用AES 需要进行内存对齐,编译器分配出来的没有对齐
		aes_in = (unsigned char *)wheapMalloc(50);
		aes_out = (unsigned char *)wheapMalloc(50);

		AES_Init(jpos_aes_key1);
		// AES 验证
		for (x = 0; x < 32; x++)
		{
			aes_in[x] = Read_Byte_UART();
		}
		AES_Decrypt(aes_out, aes_in, 32, (unsigned char *)jpos_aes_iv);
		AES_Init(jpos_aes_key2);
		AES_Encrypt(aes_out, aes_in, 32, (unsigned char *)jpos_aes_iv);
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X12);
		for (x = 0; x < 32; x++)
		{
			if (pavo_sys.jpos_support)
			{
				Write_Byte_UART(((char *)aes_in)[x]);
			}
			else
			{
				Write_Byte_UART(0);
			}
		}
		wheapFree(aes_in);
		wheapFree(aes_out);
		break;

	case 0X13:

		// 使用 AES 需要进行内存对齐,编译器分配出来的没有对齐
		aes_in = (unsigned char *)wheapMalloc(50);
		aes_out = (unsigned char *)wheapMalloc(50);

		AES_Init(aes_key1);
		// AES 验证
		for (x = 0; x < 32; x++)
		{
			aes_in[x] = Read_Byte_UART();
		}
		AES_Decrypt(aes_out, aes_in, 32, (unsigned char *)aes_iv);
		AES_Init(aes_key2);
		AES_Encrypt(aes_out, aes_in, 32, (unsigned char *)aes_iv);
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X13);
		for (x = 0; x < 32; x++)
			Write_Byte_UART(((char *)aes_in)[x]);
		wheapFree(aes_in);
		wheapFree(aes_out);
		break;

	case 0X14: // 设置正文是双行还是四行模式
		sr = Read_Byte_UART();
		if (0X01 == sr)
			pavo_sys.line_mode = 1; // 双行 line_mode = 1
		if (0X02 == sr)
			pavo_sys.line_mode = 2; // 四行 line_mode = 2
		save_system_mesg();
		Guest_clear();
		Write_Byte_UART(0X02);
		Write_Byte_UART(0X07);
		Write_Byte_UART(0X14);
		Write_Byte_UART(sr);
		break;

	case 0X15:
		sr = Read_Byte_UART();
		if (0x01 == sr)
		{
			Write_Byte_UART(0X02);
			Write_Byte_UART(0X07);
			Write_Byte_UART(0X15);
			Write_Byte_UART((unsigned char)(sizeof(SYSTEM)));
			str_temp = (char *)&pavo_sys;
			for (wtemp = 0; wtemp < sizeof(SYSTEM); wtemp++)
			{
				Write_Byte_UART(str_temp[wtemp]);
			}
		}
		if (0X27 == sr)
		{
			sr = Read_Byte_UART();
			if (0X27 == sr)
			{
				// 恢复出厂设置
				reset_to_factory();
				DogWatch_flg = 0;
			}
		}
		break;

	case 0X16:
		temp = Read_Byte_UART();
		if (0X01 == temp)
		{
			str_temp = product_name;

			while (*str_temp)
			{
				Write_Byte_UART(*str_temp++);
			}

			Write_Byte_UART(0x0);
		}
		if (0X02 == temp)
		{ // 查询软件版本
			Write_Byte_UART(TFT_VERSION);
		}

		flag = true;

		if (0X03 == temp)
		{ // 设置型号ID
			pavo_sys.mode_type = Read_Byte_UART();
			save_system_mesg();
			DogWatch_flg = 0;
		}
		if (0X04 == temp)
		{ // 设置显示风格
			pavo_sys.style = Read_Byte_UART();
			save_system_mesg();
			DogWatch_flg = 0;
		}
		if (0X05 == temp)
		{
			// 上传json结构体
			put_sysconf();
		}
		if (0X06 == temp)
		{
			for (x = 0; x < 20; x++)
			{
				temp_char = Read_UART_Char();
				if (temp_char)
				{
					pavo_sys.wel_mess1[x] = temp_char; // 将欢迎语1存储在pavo_sys.wel_mess1中
				}
				else
				{
					while (x < 20)
					{
						pavo_sys.wel_mess1[x++] = ' ';
					}
				}
			}
			save_system_mesg();
			Init_scr();
		}
		if (0X07 == temp)
		{
			for (x = 0; x < 60; x++)
			{
				temp_char = Read_UART_Char();
				if (temp_char)
				{
					pavo_sys.wel_mess2[x] = temp_char;
				}
				else
				{
					while (x < 60)
					{
						if (x < 20)
							pavo_sys.wel_mess2[x++] = ' ';
						else
							pavo_sys.wel_mess2[x++] = 0;
					}
				}
			}
			save_system_mesg();
			Init_scr();
		}
		if (0X08 == temp)
		{
			pavo_sys.temp1 = Read_Byte_UART();
			save_system_mesg();
		}
		if (0X09 == temp)
		{
			pavo_sys.temp2 = Read_Byte_UART();
			save_system_mesg();
		}
		if (0X0A == temp)
		{
			pavo_sys.Comm_No = Read_Byte_UART();
			save_system_mesg();
		}
		break;
	case 0X17:
		pic_show = Read_Byte_UART();
		if (pic_show > 0)
		{
			pic_show--;
			pic_show |= 0X80;
		}
		break;
	case 0X18:
		for (temp = 0; temp < 80; temp++)
		{
			pavo_sys.main_str[temp] = Read_UART_Char();
		}
		save_system_mesg();
		Init_scr();
		//			Init_top_show();
		break;
	case 0X19:
		// 接收两个参数
		pcc->qr_x = 0x03 & Read_Byte_UART();
		pcc->qr_y = 0x03 & Read_Byte_UART();
		// 接收点阵数据
		str_temp = (char *)wheapMalloc(192 * 192 / 8 + 32);
		if (str_temp)
		{
			for (int x = 0; x < 192 * 192 / 8; x++)
			{
				xint = Read_Byte_UART_s();
				if (xint == -1)
					break;
				str_temp[x] = xint;
			}
			event_send(e_pavo, call_show_qr_pix, pcc->qr_x, pcc->qr_y, 0, str_temp);
		}
		break;
	case 0X1A: // 全屏显示图片 n
		x = Read_Byte_UART();
		event_send(e_pavo, call_full_pic_one, x, 0, 0, 0);
		break;
	case 0X1B: // 设置每张图片的轮播
		pavo_sys.lun_time = Read_Byte_UART();
		save_system_mesg();
		break;
	case 0X1C:
		pavo_sys.into_lun_bo = Read_Byte_UART();
		save_system_mesg();
		break;
	case 0X1D:
		sr = Read_Byte_UART();
		sg = Read_Byte_UART();
		temp_mem = wheapMalloc(2888 + 8); // 记得释放
		for (int ks = 0; ks < 2888; ks++)
		{
			temp_mem[ks] = Read_Byte_UART();
			if (temp_mem[ks] == 0)
				break;
		}
		event_send(e_pavo, call_show_qr, 0, sr, 0, temp_mem); // 发送正文显示二维码事件
		break;
	case 0X1E:
		sr = Read_Byte_UART();
		sg = Read_Byte_UART();
		temp_mem = wheapMalloc(2888 + 8); // 记得释放
		for (int ks = 0; ks < 2888; ks++)
		{
			temp_mem[ks] = Read_Byte_UART();
			if (temp_mem[ks] == 0)
				break;
		}
		event_send(e_pavo, call_show_qr, 1, sr, sg, temp_mem); // 发送播放图片后显示二维码事件
		break;
	case 0X1F:
		//			Read_Byte_UART();
		//			pavo_sys.bm_rolly_speed = Read_Byte_UART();
		//			if ( pavo_sys.bm_rolly_speed > 9 ) pavo_sys.bm_rolly_speed = 9;
		//			save_system_mesg();
		break;

	case 0x20:
		dtemp[0] = Read_Byte_UART();
		if (dtemp[0] == 1)
		{
			dtemp[1] = Read_Byte_UART();
			if (dtemp[1] == 0x01)
			{
				pavo_sys.jpos_support = Read_Byte_UART();
				save_system_mesg();
			}
		}
		break;

	case 0X27:
		dtemp[0] = Read_Byte_UART();
		dtemp[1] = Read_Byte_UART();
		if (0X27 == dtemp[0] && 0X27 == dtemp[1])
			pcc->get_file = 1;
		if (0X26 == dtemp[0] && 0X26 == dtemp[1])
			pcc->put_file = 1;
		break;
	case 0X28:
		dtemp[0] = Read_Byte_UART();
		dtemp[1] = Read_Byte_UART();
		pavo_sys.pic_reg = (dtemp[0] << 8) | dtemp[1];
		save_system_mesg();
		break;
	}
}

void InitSerial_9600(void)
{
}

void display_line(byte *str, byte line)
{
	byte temp_d_x;

	for (temp_d_x = 0; temp_d_x < 20; temp_d_x++)
	{
		if (*str)
		{
			S_text.Textbuf_show[line][temp_d_x] = 0;
			S_text.Textbuf[line][temp_d_x] = *str++;
		}
		else
		{
			S_text.Textbuf_show[line][temp_d_x] = 0;
			S_text.Textbuf[line][temp_d_x] = ' ';
		}
	}
	S_text.Change_flg = 1;
	// delay_ms(1000);
}

// 设置背光亮度
void set_back_light(int light)
{
	switch (light)
	{
	case 0:
		BL_Unit_Config(0);
		break;
	case 1:
		BL_Unit_Config(95);
		break;
	case 2:
		BL_Unit_Config(85);
		break;
	case 3:
		BL_Unit_Config(75);
		break;
	case 4:
		BL_Unit_Config(70);
		break;
	case 5:
		BL_Unit_Config(60);
		break;
	case 6:
		BL_Unit_Config(50);
		break;
	case 7:
		BL_Unit_Config(30);
		break;
	default:
		BL_Unit_Config(95);
		break;
	}
}

/**
 * @brief 	  初始化系统信息
 * @param     无
 * @retval    无
 */
void Init_system_mesg(void)
{
	if (false == read_system_mesg())
		reset_to_factory(); // 如果读取错误会自动恢复出厂设置
	switch (pavo_sys.Baud_Rate)
	{
	case 0: // 9600
		InitSerial(9600);
		break;

	case 1: // 19200
		InitSerial(19200);
		break;

	case 2: // 38400
		InitSerial(38400);
		break;

	case 3: // 57600
		InitSerial(57600);
		break;

	case 4: // 115200
		InitSerial(115200);
		break;

	default: // 9600
		pavo_sys.Baud_Rate = 0;
		InitSerial(9600);
		break;
	}
	if (pavo_sys.back_light == 0)
		pavo_sys.back_light = 5;
}

// 遇到这13个特殊内码，要根据国家进行替换。
#define fxChar_U_S_A 0
#define fxChar_FRANCE 1
#define fxChar_GERMANY 2
#define fxChar_U_K_ 3
#define fxChar_DENMARK_I 4
#define fxChar_SWEDEN 5
#define fxChar_ITALY 6
#define fxChar_SPAIN 7
#define fxChar_JAPAN 8
#define fxChar_NORWAY 9
#define fxChar_DENMARK 10
#define fxChar_SLAVONIC 11
#define fxChar_RUSSIA 12

// 替换一个字符的点阵数据
void Fix_char(P_Font *font, int where, char *data)
{
	memcpy(&font->font_data_buff[where * (font->font_height * font->font_wide / 8)], data, font->font_height * font->font_wide / 8);
}

void Fix_pfont()
{
	char *fx_main_font_name[] = {"40x80_fxChar_U_S_A.bin", "40x80_fxChar_FRANCE.bin", "40x80_fxChar_GERMANY.bin", "40x80_fxChar_U_K.bin", "40x80_fxChar_DENMARK_I.bin", "40x80_fxChar_SWEDEN.bin", "40x80_fxChar_ITALY.bin", "40x80_fxChar_SPAIN.bin", "40x80_fxChar_JAPAN.bin", "40x80_fxChar_NORWAY.bin", "40x80_fxChar_DENMARK.bin", "40x80_fxChar_SLAVONIC.bin", "40x80_fxChar_RUSSIA.bin"};
	char *fx_mini_font_name[] = {"24x48_fxChar_U_S_A.bin", "24x48_fxChar_FRANCE.bin", "24x48_fxChar_GERMANY.bin", "24x48_fxChar_U_K.bin", "24x48_fxChar_DENMARK_I.bin", "24x48_fxChar_SWEDEN.bin", "24x48_fxChar_ITALY.bin", "24x48_fxChar_SPAIN.bin", "24x48_fxChar_JAPAN.bin", "24x48_fxChar_NORWAY.bin", "24x48_fxChar_DENMARK.bin", "24x48_fxChar_SLAVONIC.bin", "24x48_fxChar_RUSSIA.bin"};

	lfs_file_t fp_main, fp_mini; // 文件对象

	int idx_fx[12] = {0X23, 0X24, 0X40, 0X5B, 0X5C, 0X5D, 0X5E, 0X60, 0X7B, 0X7C, 0X7D, 0X7E};
	int fix_main_size = 12 * (main_font->font_height * main_font->font_wide) / 8;
	int fix_mini_size = 12 * (mini_x_font->font_height * mini_x_font->font_wide) / 8;
	char *fix_main_buf = wheapMalloc(fix_main_size);
	char *fix_mini_buf = wheapMalloc(fix_mini_size);
	char *data_ptr;
	unsigned int rt;

	if (pavo_sys.Char_No < 13 && main_font && mini_x_font && pavo_sys.Expand_Char_No != 0X19 && pavo_sys.Expand_Char_No != 0X92)
	{
		char *mini_file = fx_mini_font_name[pavo_sys.Char_No];
		char *main_file = fx_main_font_name[pavo_sys.Char_No];

		// 修改主字库
		if (lfs_file_open(&lfs, &fp_main, main_file, LFS_O_RDONLY) < 0)
		{
			printf("fix main char error, file < %s > cannot open \r\n", main_file);
		}
		else
		{
			rt = lfs_file_read(&lfs, &fp_main, fix_main_buf, fix_main_size);
			for (int x = 0; x < 12; x++)
			{
				data_ptr = &fix_main_buf[x * (main_font->font_height * main_font->font_wide) / 8];
				Fix_char(main_font, idx_fx[x], data_ptr);
			}
			lfs_file_close(&lfs, &fp_main);
		}

		// 修改迷你字库
		if (lfs_file_open(&lfs, &fp_mini, mini_file, LFS_O_RDONLY) < 0)
		{
			printf("fix mini char error ,file < %s > cannot open \r\n", mini_file);
		}
		else
		{
			rt = lfs_file_read(&lfs, &fp_mini, fix_mini_buf, fix_mini_size);
			for (int x = 0; x < 12; x++)
			{
				data_ptr = &fix_mini_buf[x * (mini_x_font->font_height * mini_x_font->font_wide) / 8];
				Fix_char(mini_x_font, idx_fx[x], data_ptr);
			}
			lfs_file_close(&lfs, &fp_mini);
		}
	}
	wheapFree(fix_main_buf);
	wheapFree(fix_mini_buf);
}

// 加载字库文件
uint16_t *big_to_gbk_table = 0;
void Load_font(void)
{
	lfs_file_t file;
	unsigned int read_coute = 0;
	if (system_font)
		del_pfont(system_font);
	if (main_font)
		del_pfont(main_font);
	if (mini_x_font)
		del_pfont(mini_x_font);
	if (GBK_font)
		del_pfont(GBK_font);
	if (big_to_gbk_table)
		wheapFree(big_to_gbk_table);

	// 加载映射表
	if (lfs_file_open(&lfs, &file, "big52gbk.bin", LFS_O_RDONLY) < 0)
	{
		printf("big52gbk.bin Open Error!\r\n");
	}
	else
	{
		big_to_gbk_table = (uint16_t *)wheapMalloc(lfs_file_size(&lfs, &file) + 100);
		read_coute = lfs_file_read(&lfs, &file, big_to_gbk_table, lfs_file_size(&lfs, &file));
		lfs_file_close(&lfs, &file);
	}

	// 加载系统字体(437字库)
	system_font = Init_pfont("437_48x24.bin", 24, 48);
	M_font_color_set(system_font, mRGB(255, 255, 255), mRGB(120, 120, 120));

	// 字符集
	switch (pavo_sys.Expand_Char_No)
	{
	case 59:
		break;

	case 0:
		strcpy(pcc->cur_font_name, "CP437");
		main_font = Init_pfont("437_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("437_48x24.bin", 24, 48);
		break;

	case 1:
		strcpy(pcc->cur_font_name, "Katakana");
		main_font = Init_pfont("Katakana_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("Katakana_48x24.bin", 24, 48);
		break;

	case 2:
		strcpy(pcc->cur_font_name, "CP850");
		main_font = Init_pfont("850_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("850_48x24.bin", 24, 48);
		break;

	case 3:
		strcpy(pcc->cur_font_name, "CP860");
		main_font = Init_pfont("860_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("860_48x24.bin", 24, 48);
		break;

	case 4:
		strcpy(pcc->cur_font_name, "CP863");
		main_font = Init_pfont("863_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("863_48x24.bin", 24, 48);
		break;

	case 5:
		strcpy(pcc->cur_font_name, "CP865");
		main_font = Init_pfont("865_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("865_48x24.bin", 24, 48);
		break;

	case 6:
		strcpy(pcc->cur_font_name, "Slavonic");
		main_font = Init_pfont("852_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("852_48x24.bin", 24, 48);
		break;

	case 7:
		strcpy(pcc->cur_font_name, "CP866");
		main_font = Init_pfont("866_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("866_48x24.bin", 24, 48);
		break;
	case 8:
		strcpy(pcc->cur_font_name, "WPC1252");
		main_font = Init_pfont("1252_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1252_48x24.bin", 24, 48);
		break;
	case 9:
		strcpy(pcc->cur_font_name, "CP857");
		main_font = Init_pfont("857_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("857_48x24.bin", 24, 48);
		break;
	case 0xA:
		strcpy(pcc->cur_font_name, "polish");
		main_font = Init_pfont("polish_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("polish_48x24.bin", 24, 48);
		break;
	case 0X0B:
		strcpy(pcc->cur_font_name, "WCP1250");
		main_font = Init_pfont("1250_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1250_48x24.bin", 24, 48);
		break;
		//		case 0X0C :
		//
		//			break;
	case 0X0E:
		strcpy(pcc->cur_font_name, "WCP1251");
		main_font = Init_pfont("1251_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1251_48x24.bin", 24, 48);
		break;
	case 0X0F:
		strcpy(pcc->cur_font_name, "CP866_AT");
		main_font = Init_pfont("866_AT_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("866_AT_48x24.bin", 24, 48);
		break;
	case 0X10:
		strcpy(pcc->cur_font_name, "WPC1252");
		main_font = Init_pfont("1252_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1252_48x24.bin", 24, 48);
		break;
	case 0X11:
		strcpy(pcc->cur_font_name, "CP866");
		main_font = Init_pfont("866_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("866_48x24.bin", 24, 48);
		break;
	case 0X12:
		strcpy(pcc->cur_font_name, "CP852");
		main_font = Init_pfont("852_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("852_48x24.bin", 24, 48);
		break;
	case 0X13:
		strcpy(pcc->cur_font_name, "CP858");
		main_font = Init_pfont("858_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("858_48x24.bin", 24, 48);
		break;
	case 0X14:
		strcpy(pcc->cur_font_name, "Farsi");
		main_font = Init_pfont("Farsi_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("Farsi_48x24.bin", 24, 48);
		break;
	case 0X15:
		strcpy(pcc->cur_font_name, "CP737");
		main_font = Init_pfont("737_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("737_48x24.bin", 24, 48);
		break;
	case 0X16:
		strcpy(pcc->cur_font_name, "CP864");
		main_font = Init_pfont("864_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("864_48x24.bin", 24, 48);
		break;
	case 0X17:
		strcpy(pcc->cur_font_name, "CP737_S");
		main_font = Init_pfont("737S_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("737S_48x24.bin", 24, 48);
		break;
	case 0X18:
		strcpy(pcc->cur_font_name, "CP1256");
		main_font = Init_pfont("1256_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1256_48x24.bin", 24, 48);
		break;
	case 0X19: // 中文字体
		strcpy(pcc->cur_font_name, "GBK");
		GBK_font = Init_pfont("936_gbk_40x40.bin", 40, 40);
		mini_x_font = Init_pfont("437_48x24.bin", 24, 48);
		main_font = Init_pfont("437_40x40.bin", 40, 40);
		break;
	case 0X92: // 中文字体
		strcpy(pcc->cur_font_name, "BIG5");
		GBK_font = Init_pfont("936_gbk_40x40.bin", 40, 40);
		mini_x_font = Init_pfont("437_48x24.bin", 24, 48);
		main_font = Init_pfont("437_40x40.bin", 40, 40);
		break;

	case 0X2F:
		strcpy(pcc->cur_font_name, "WCP1253");
		main_font = Init_pfont("1253_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1253_48x24.bin", 24, 48);
		break;
	case 0X31:
		strcpy(pcc->cur_font_name, "WCP1255");
		main_font = Init_pfont("1255_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1255_48x24.bin", 24, 48);
		break;
	case 0X33:
		strcpy(pcc->cur_font_name, "WCP1257");
		main_font = Init_pfont("1257_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1257_48x24.bin", 24, 48);
		break;
	case 0X34:
		strcpy(pcc->cur_font_name, "WCP1258");
		main_font = Init_pfont("1258_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1258_48x24.bin", 24, 48);
		break;
	case 0X35:
		strcpy(pcc->cur_font_name, "WCP1257S");
		main_font = Init_pfont("1257S_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1257S_48x24.bin", 24, 48);
		break;
	case 0X36:
		strcpy(pcc->cur_font_name, "ISO 8859-7");
		main_font = Init_pfont("737-S1_28597_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("737-S1_28597_48x24.bin", 24, 48);
		break;
	case 27:
		strcpy(pcc->cur_font_name, "CP862");
		main_font = Init_pfont("862_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("862_48x24.bin", 24, 48);
		break;
	case 0X37:
		strcpy(pcc->cur_font_name, "CP874");
		main_font = Init_pfont("874_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("874_48x24.bin", 24, 48);
		break;
	case 0X38:
		strcpy(pcc->cur_font_name, "ISO8859-8");
		main_font = Init_pfont("8859-8_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("8859-8_48x24.bin", 24, 48);
		break;
	case 32:
		strcpy(pcc->cur_font_name, "CP855");
		main_font = Init_pfont("855_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("855_48x24.bin", 24, 48);
		break;
	case 50:
		strcpy(pcc->cur_font_name, "CP1254");
		main_font = Init_pfont("1254_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("1254_48x24.bin", 24, 48);
		break;

	default:
		strcpy(pcc->cur_font_name, "CP437");
		main_font = Init_pfont("437_80x40.bin", 40, 80);
		mini_x_font = Init_pfont("437_48x24.bin", 24, 48);
		break;
	}
	Fix_pfont(); // 修复那13个国家的不同
}

void Initsys(void)
{
	DogWatch_flg = 1;
	S_text.Display_modle = 1; // 显示模式
	S_text.Cursor = 0;		  // 光标位置
	S_text.Cursor_flg = 0;	  // 光标关

	//	screen.Dis_page =0;
	//	screen.Buf_page =1;
	//  screen.col = 0;

	S_text.Change_flg = 0;

	S_text.Top_line_roll_flg = 0; // 顶行滚动显示标志
	S_text.Roll_num = 0x07ff;
	S_text.Roll_t = 0;

	flashing.flag = 0; // 闪烁标志
	flashing.freq = 2;
	Iden_flg = 0;		  // 空闲标志
	Iden_number = 0x00ff; // 5s进入屏幕欢迎语
	TIME0_number = 0;

	//	pavo_clock.flg = 0;
	//	pavo_clock.display_flg = 0;
	//	pavo_clock.seconds = 0;
	//	clock.minutes = 0;
	//	clock.hours = 0;
	//	Time_number = 0xec8f;

	Init_system_mesg(); // 波特率\命令集\标准字符集\扩展字符集

	Rolling_Screen_flg = 0;

	MCUCTS(0); // 硬件握手，始终有效

	COM_CTS_flg = 0; // 串口忙标志清0，表示不忙
	COM_CTS_num = 0;

	StartDall = 0;
	StartDall_flg = 0; // 开机是否延时，0--不延时

	CursorMode3LineNo = 0;

	if (pavo_sys.Baud_Rate == 0)
		InitSerial(9600);
	else if (pavo_sys.Baud_Rate == 1)
		InitSerial(19200);
	else if (pavo_sys.Baud_Rate == 2)
		InitSerial(38400);
	else if (pavo_sys.Baud_Rate == 3)
		InitSerial(57600);
	else if (pavo_sys.Baud_Rate == 4)
		InitSerial(115200);
	else
		InitSerial(9600);

	// 载入字库
}

void MCUCTS(char dir) // 硬件握手，始终有效
{
}

void Init_tp(void)
{
	char *abuf = wheapMalloc(100);
	if (!abuf)
	{
		printf("Init_tp: abuf Memory allocation failed!\r\n");
		return;
	}

#if (scr_6_2_inch_960_360 == 1)
	// 显示迷你logo
	// 显示图片还是文本
	// printf("pavo_sys.tp_mode = 0x%X\r\n", pavo_sys.tp_mode);

	if (pavo_sys.tp_mode == 0X09) // 0x09 显示图片
	{
		PAVO_Show_picture("head_pic.jpg", 400, 0, 960 - 400, 70);
	}
	else // 0x0A 显示文本
	{
		// 设置字体颜色
		M_font_color_set(mini_x_font, mRGB(pavo_sys.tp_font_r, pavo_sys.tp_font_g, pavo_sys.tp_font_b), mRGB(pavo_sys.tp_back_r, pavo_sys.tp_back_g, pavo_sys.tp_back_b));
		// 填充背景区域
		pavo_fill_area(400, 0, 960 - 400, 70, mRGB(pavo_sys.tp_back_r, pavo_sys.tp_back_g, pavo_sys.tp_back_b));
		for (int x = 0; x < 21; x++)
		{
			abuf[x] = pavo_sys.wel_mess1[x];
		}
		M_Show_String(mini_x_font, 400 + 10, 12, abuf); // 顶部显示文本
	}
	PAVO_Show_picture("logo_m0.jpg", 0, 0, 400, 70);
#endif

	wheapFree(abuf);
}

// 初始化底层显示区
void Init_bm(void)
{
	char *abuf = wheapMalloc(100);
	if (!abuf)
	{
		printf("Init_bm: abuf Memory allocation failed!\r\n");
		return;
	}
	pavo_sys.wel_mess2[60] = 0; // 防止溢出

	printf("Init_bm: pavo_sys.style = %d\r\n", pavo_sys.style);
	if (pavo_sys.style == 1)
	{
		for (int x = 0; x <= 60; x++)
		{
			abuf[x] = (char)pavo_sys.wel_mess2[x];
		}

		pavo_fill_area(0, 360 - 70, 960, 70, mRGB(pavo_sys.bm_back_r, pavo_sys.bm_back_g, pavo_sys.bm_back_b));
		M_font_color_set(mini_x_font, mRGB(pavo_sys.bm_font_r, pavo_sys.bm_font_g, pavo_sys.bm_font_b), mRGB(pavo_sys.bm_back_r, pavo_sys.bm_back_g, pavo_sys.bm_back_b));

		//		Init_rolly_text(rt_wellcome, mini_x_font, (char *) pavo_sys.wel_mess2);
		//		rolly_bind(scr_main, rt_wellcome, 0, 480 - 60, 800);

		// 底部滚动实现
		if (rt_wellcome)
			del_rolly(rt_wellcome); // 删除欢迎语滚动器

		// 创建新的滚动对象
		rt_wellcome = new_rolly_bind(0, 360 - 60, 960, mini_x_font, abuf);
		rt_wellcome->rolly_coute = 0;
		do_rolly(rt_wellcome); // 执行一次滚动

		// create_rolly_timer();
	}

	if (pavo_sys.style == 2)
	{

		//		if (rt_wellcome)
		//			del_rolly(rt_wellcome);
		//
		//		rt_wellcome = new_rolly_bind2(scr_main, 0, 62, 800, main_font, pavo_sys.wel_mess2);
		//		rt_wellcome->rolly_coute = 0;
		//		do_rolly(rt_wellcome);
	}

	if (pavo_sys.style == 3)
	{
		for (int x = 0; x <= 60; x++)
		{
			abuf[x] = (char)pavo_sys.wel_mess2[x];
		}

		if (rt_wellcome)
			del_rolly(rt_wellcome);
		// rt_wellcome = new_rolly_bind(scr_main, 0, 80, 800, main_font, abuf);
		rt_wellcome = new_rolly_bind(0, 80, 960, main_font, abuf);
		rt_wellcome->rolly_coute = 0;
		do_rolly(rt_wellcome);
	}

	wheapFree(abuf);
}

// 初始化中间显示模式
void Init_top_show(void)
{
	if (pavo_sys.line_mode == 1) // 双行模式处理（20x2显示）, 显示2行
	{
		for (int x = 0; x < 40; x++)
			S_text.Textbuf[x / 20][x % 20] = pavo_sys.main_str[x];
	}
	else // 四行模式处理（20x4显示）, 显示4行
	{
		for (int x = 0; x < 80; x++)
			S_text.Textbuf[x / 20][x % 20] = pavo_sys.main_str[x];
	}
}

/**
 * @brief    初始化欢迎界面
 * @param
 * @retval   无
 */
void Init_scr(void)
{
	pavo_sys.wel_mess2[60] = 0;

	// printf("pavo_sys.style = %d\r\n", pavo_sys.style);

	if (pavo_sys.style == 1) // 5寸传统英文
	{
		Init_tp();				// 初始化顶行
		Init_top_show();		// 初始化中间显示模式
		update_Show_customer(); // 更新客显中间内容

		Init_bm(); // 初始化底层显示区
	}
	if (pavo_sys.style == 2)
	{
		for (int x = 0; x < 20; x++)
		{
			S_text.Textbuf[0][x] = pavo_sys.wel_mess1[x];
			S_text.Textbuf[1][x] = pavo_sys.wel_mess2[x];
		}
		update_Show_customer();

		// 初始化底层显示区
		Init_bm();
		PAVO_Show_picture("Guest.jpg", 0, 120, LCD_XSIZE_TFT, LCD_YSIZE_TFT - 120); // 显示数据写入缓冲
	}

	if (pavo_sys.style == 3)
	{
		for (int x = 0; x < 20; x++)
		{
			S_text.Textbuf[0][x] = pavo_sys.wel_mess1[x];
			S_text.Textbuf[1][x] = pavo_sys.wel_mess2[x];
		}
		update_Show_customer();

		// 初始化底层显示区
		Init_bm();
		PAVO_Show_picture("Guest.jpg", 0, 160, LCD_XSIZE_TFT, LCD_YSIZE_TFT - 160); // 显示数据写入缓冲
	}
}

void Guest_clear(void)
{
}

/**
 * @brief 	  填充指定区域颜色
 * @param
 * @retval    无
 */
void pavo_fill_area(int start_x, int start_y, int wide, int hight, u16 fill_color)
{
	if (start_y + hight > LCD_YSIZE_TFT || start_x + wide > LCD_XSIZE_TFT)
		return;

	lv_color_t lv_color = LV_COLOR_MAKE(
		(fill_color >> 11) << 3,		 // R 5bit转8bit
		((fill_color >> 5) & 0x3F) << 2, // G 6bit转8bit
		(fill_color & 0x1F) << 3		 // B 5bit转8bit
	);

	for (int y = start_y; y < start_y + hight; y++)
	{
		for (int x = start_x; x < start_x + wide; x++)
		{
			// cur_paint_scr->pix_dat[x + y * LCD_XSIZE_TFT] = fill_color;
			lv_canvas_set_px(g_canvas, x, y, lv_color);
		}
	}
}

void pavo_fill_down_area(int start_x, int start_y, int wide, int hight, u16 fill_color)
{
	if (start_y + hight > LCD_YSIZE_TFT || start_x + wide > LCD_XSIZE_TFT)
		return;

	lv_color_t lv_color = LV_COLOR_MAKE(
		(fill_color >> 11) << 3,		 // R 5bit转8bit
		((fill_color >> 5) & 0x3F) << 2, // G 6bit转8bit
		(fill_color & 0x1F) << 3		 // B 5bit转8bit
	);

	for (int y = start_y; y < start_y + hight; y++)
	{
		for (int x = start_x; x < start_x + wide; x++)
		{
			// cur_paint_scr->pix_dat[x + y * LCD_XSIZE_TFT] = fill_color;
			lv_canvas_set_px(down_canvas, x, y, lv_color);
		}
	}
}

// static u8 table_5bit[0X1F],table_6bit[0X3F];   //RGB 565 转换成  RGB888
u32 to_RGB_888(u16 b565)
{
	// static char init_flg = 0;
	u32 ret;

	ret = (((b565 >> 11) & 0x1f) * 255) / 0X1F;
	ret <<= 8;

	ret |= (((b565 >> 5) & 0x3f) * 255) / 0X3F;
	ret <<= 8;

	ret |= (((b565) & 0x1f) * 255) / 0X3F;

	return ret;
}

unsigned int RGB565ToRGB888(unsigned short n565Color)
{
	unsigned int n888Color = 0;

	// 获取RGB单色，并填充低位
	unsigned char cRed = (n565Color & RGB565_RED) >> 8;
	unsigned char cGreen = (n565Color & RGB565_GREEN) >> 3;
	unsigned char cBlue = (n565Color & RGB565_BLUE) << 3;

	// 连接
	n888Color = (cRed << 16) + (cGreen << 8) + (cBlue << 0);
	return n888Color;
}

unsigned short RGB888ToRGB565(unsigned int n888Color)
{
	unsigned short n565Color = 0;

	// 获取RGB单色，并截取高位
	unsigned char cRed = (n888Color & RGB888_RED) >> 19;
	unsigned char cGreen = (n888Color & RGB888_GREEN) >> 10;
	unsigned char cBlue = (n888Color & RGB888_BLUE) >> 3;

	// 连接
	n565Color = (cRed << 11) + (cGreen << 5) + (cBlue << 0);
	return n565Color;
}

void switch_paint_scr(SCR *where_scr)
{
	cur_paint_scr = where_scr;
}

/**
 * @brief    显示图片
 * @param
 * @retval   无
 */
void PAVO_Show_picture(char *file_name, int start_x, int start_y, int wide, int height)
{
	IODEV *xv; // 解码出来的数据
	xv = jpeg_Decode(file_name);

	if (xv)
	{
		// 获取图片实际宽度和高度
		if (height > xv->p_height)
		{
			height = xv->p_height;
		}
		if (wide > xv->p_wide)
		{
			wide = xv->p_wide;
		}

		char *pd = (char *)xv->frmbuf;
		for (int hy = 0; hy < height; hy++)
		{
			for (int hx = 0; hx < wide; hx++)
			{
				lv_canvas_set_px(g_canvas, start_x + hx, start_y + hy, lv_color_make(pd[0], pd[1], pd[2]));
				pd += 3;
			}
		}
		iodev_free(xv); // 记得释放
	}
	else
	{
		printf("jpeg fail < %s >\r\n", file_name);
	}
}

/**
 * @brief 	  保存系统信息
 * @param     无
 * @retval    bool
 */
char system_Writing_flg = 0;
bool save_system_mesg()
{
	lfs_file_t fp;
	unsigned int write_coute = 0;
	if (lfs_file_open(&lfs, &fp, "system.ini", LFS_O_CREAT | LFS_O_WRONLY) < 0)
	{
		printf("connot open system.ini \r\n");
	}
	else
	{
		write_coute = lfs_file_write(&lfs, &fp, &pavo_sys.temp1, sizeof(SYSTEM));
		if (write_coute == sizeof(SYSTEM))
		{
			printf("system.ini have been write!\r\n");
		}
		else
		{
			printf("write system.ini error\r\n");
		}
		lfs_file_close(&lfs, &fp);
		return true;
	}
	return false;
}

/**
 * @brief 	  读取系统信息
 * @param     无
 * @retval    byte(unsigned char)
 */
byte read_system_mesg()
{
	byte ret = false;
	void *vbuf = wheapMalloc(sizeof(SYSTEM));
	lfs_file_t fp;
	unsigned int read_coute;

	if (lfs_file_open(&lfs, &fp, "system.ini", LFS_O_RDONLY) < 0)
	{
		printf("<system.ini> read fail \r\n");
	}
	else
	{
		// 读取文件内容
		read_coute = lfs_file_read(&lfs, &fp, vbuf, sizeof(SYSTEM));
		if (sizeof(SYSTEM) != read_coute)
		{
			printf("<system.ini> size fail \r\n");
		}
		else
		{
			pavo_sys = *(SYSTEM *)vbuf;
			ret = true;
		}
		lfs_file_close(&lfs, &fp);
	}

	wheapFree(vbuf);
	return ret;
}

void m_strcpy(char *dst, char *src)
{
	while (*src)
		*dst++ = *src++;
}

void reset_to_factory(void)
{
	int x;
	pavo_sys.temp1 = 0;
	pavo_sys.Baud_Rate = 0;
	pavo_sys.back_light = 5;
	pavo_sys.Char_No = 0x00;
	pavo_sys.Comm_No = 0XFF; // 默认是AUTO
	pavo_sys.temp2 = 0X00;
	pavo_sys.Expand_Char_No = 0;
	pavo_sys.line_mode = 1; // 双行模式
	pavo_sys.tp_mode = 0x0A;

	char *pstr1 = "*** TFT DISPLAY *** ";
	for (x = 0; x < 20 && *pstr1; x++)
	{
		pavo_sys.wel_mess1[x] = *pstr1++;
	}
	pavo_sys.wel_mess1[x] = 0;

	char *pstr2 = "**HAVE A NICE DAY AND THANK YOU**";
	for (x = 0; x < 60 && *pstr2; x++)
	{
		pavo_sys.wel_mess2[x] = *pstr2++;
	}
	pavo_sys.wel_mess2[x] = 0;

	char *pstr = "*** TFT DISPLAY ***   HAVE A NICE DAY!    ";
	for (x = 0; x < 80 && pstr[x]; x++)
	{
		pavo_sys.main_str[x] = pstr[x];
	}

	pavo_sys.main_str[x] = 0;
	pavo_sys.tp_font_r = 0XFF;
	pavo_sys.tp_font_g = 0XFF;
	pavo_sys.tp_font_b = 0XFF;
	pavo_sys.tp_back_r = 0x00;
	pavo_sys.tp_back_g = 0x00;
	pavo_sys.tp_back_b = 0xFF;
	pavo_sys.font_pc_color = mRGB(0X0, 0XFF, 0XFF);
	pavo_sys.back_pc_color = mRGB(0X3D, 0X3D, 0X3D);
	pavo_sys.font_Foreground_color = mRGB(0X0, 0XFF, 0XFF); // 系统正文背景颜色
	pavo_sys.font_Background_color = mRGB(0X0, 0X0, 0X0);	// 系统正文字体颜色
	pavo_sys.bm_back_b = 0XFF;
	pavo_sys.bm_back_g = 0XFF;
	pavo_sys.bm_back_r = 0XFF;
	pavo_sys.bm_font_r = 0X0;
	pavo_sys.bm_font_g = 0X0;
	pavo_sys.bm_font_b = 0XFF;
	pavo_sys.pic_reg = 0X3FF;
	pavo_sys.into_lun_bo = 0; // 不轮播
	pavo_sys.bm_rolly_speed = 8;

	save_system_mesg(); // 保存系统信息
						// DogWatch_flg = 0;
}
