<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>FreeRTOS_LVGL</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6230000::V6.23::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>ARMCM4</Device>
          <Vendor>ARM</Vendor>
          <PackID>ARM.CMSIS.5.9.0</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IROM(0x00000000,0x00040000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ESEL ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000)</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:ARMCM4$Device\ARMCM4\Include\ARMCM4.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile></SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>Freertos_LVGL</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>D:\Software\Keil_v5\MDK\Keil_v5\ARM\ARMCLANG\bin\fromelf.exe .\Objects\Freertos_LVGL.axf --bin -o .\Objects\wen.bin</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>1</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x40000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8010000</StartAddress>
                <Size>0x1f0000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x200000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>0</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>0</vShortEn>
            <vShortWch>0</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls>-include global-config.h -fdebug-macro -Wno-invalid-source-encoding</MiscControls>
              <Define>USE_STDPERIPH_DRIVER,LV_LVGL_H_INCLUDE_SIMPLE,SELECT_DEMO_WIDGETS</Define>
              <Undefine></Undefine>
              <IncludePath>..\Libraries\CMSIS\Include;..\Libraries\Device\MegaHunt\MH245x\Include;..\Libraries\MH245x_StdPeriph_Driver\inc;..\Libraries\RTOS\freertos\11.1.0\include;..\Libraries\RTOS\freertos\11.1.0\portable\GCC\ARM_CM4F;..\Libraries\OSS\lvgl\8.4.0;..\Libraries\OSS\lvgl\8.4.0\demos;..\BSP;..\FreeRTOSConfig\Config;..\LVGL;..\user;..\HW;..\HW\wheap;..\Wen_lib;..\Fck_src;..\LibCRC;..\Font_table;..\Libraries\OSS\lvgl\8.4.0\src\core;..\ZLG_GUI;..\Lib;..\Machine;..\Include;..\Myapp;..\Arch_arm32;..\Driver;..\Lfs;..\Tjpgd</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>Startup</GroupName>
          <Files>
            <File>
              <FileName>startup_mh245x.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\Libraries\Device\MegaHunt\MH245x\Source\ARM\startup_mh245x.s</FilePath>
            </File>
            <File>
              <FileName>system_mh2457.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\Device\MegaHunt\MH245x\Source\system_mh2457.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\main.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\mh245x_it.c</FilePath>
            </File>
            <File>
              <FileName>run_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\run_led.c</FilePath>
            </File>
            <File>
              <FileName>app_main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\app_main.c</FilePath>
            </File>
            <File>
              <FileName>app_main.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\user\app_main.h</FilePath>
            </File>
            <File>
              <FileName>hx_proto.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\hx_proto.c</FilePath>
            </File>
            <File>
              <FileName>hx_proto.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\user\hx_proto.h</FilePath>
            </File>
            <File>
              <FileName>font_example.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\font_example.c</FilePath>
            </File>
            <File>
              <FileName>font_example.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\user\font_example.h</FilePath>
            </File>
            <File>
              <FileName>global-config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\user\global-config.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>HW</GroupName>
          <Files>
            <File>
              <FileName>HW.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HW\HW.c</FilePath>
            </File>
            <File>
              <FileName>HW.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\HW\HW.h</FilePath>
            </File>
            <File>
              <FileName>Usart3.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HW\Usart3.c</FilePath>
            </File>
            <File>
              <FileName>Usart3.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\HW\Usart3.h</FilePath>
            </File>
            <File>
              <FileName>track_fun_AES.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HW\track_fun_AES.c</FilePath>
            </File>
            <File>
              <FileName>track_fun_AES.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\HW\track_fun_AES.h</FilePath>
            </File>
            <File>
              <FileName>Keyboard.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HW\Keyboard.c</FilePath>
            </File>
            <File>
              <FileName>Keyboard.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\HW\Keyboard.h</FilePath>
            </File>
            <File>
              <FileName>Iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\HW\Iwdg.c</FilePath>
            </File>
            <File>
              <FileName>Iwdg.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\HW\Iwdg.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Wheap</GroupName>
          <Files>
            <File>
              <FileName>tlsf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Wheap\tlsf.c</FilePath>
            </File>
            <File>
              <FileName>tlsf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wheap\tlsf.h</FilePath>
            </File>
            <File>
              <FileName>wheap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Wheap\wheap.c</FilePath>
            </File>
            <File>
              <FileName>wheap.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wheap\wheap.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Tjpgd</GroupName>
          <Files>
            <File>
              <FileName>jdev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Tjpgd\jdev.c</FilePath>
            </File>
            <File>
              <FileName>jdev.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Tjpgd\jdev.h</FilePath>
            </File>
            <File>
              <FileName>tjpgd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Tjpgd\tjpgd.h</FilePath>
            </File>
            <File>
              <FileName>tjpgd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Tjpgd\tjpgd.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Lfs</GroupName>
          <Files>
            <File>
              <FileName>diskio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Lfs\diskio.h</FilePath>
            </File>
            <File>
              <FileName>diskio_mh2457.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lfs\diskio_mh2457.c</FilePath>
            </File>
            <File>
              <FileName>lfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lfs\lfs.c</FilePath>
            </File>
            <File>
              <FileName>lfs.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Lfs\lfs.h</FilePath>
            </File>
            <File>
              <FileName>lfs_util.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lfs\lfs_util.c</FilePath>
            </File>
            <File>
              <FileName>lfs_util.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Lfs\lfs_util.h</FilePath>
            </File>
            <File>
              <FileName>lfs_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lfs\lfs_test.c</FilePath>
            </File>
            <File>
              <FileName>lfs_speed_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lfs\lfs_speed_test.c</FilePath>
            </File>
            <File>
              <FileName>lfs_speed_test.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Lfs\lfs_speed_test.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Arch_arm32</GroupName>
          <Files>
            <File>
              <FileName>types.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Arch_arm32\types.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Include</GroupName>
          <Files>
            <File>
              <FileName>sizes.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Include\sizes.h</FilePath>
            </File>
            <File>
              <FileName>io.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Include\io.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Lib</GroupName>
          <Files>
            <File>
              <FileName>malloc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lib\malloc.c</FilePath>
            </File>
            <File>
              <FileName>malloc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Lib\malloc.h</FilePath>
            </File>
            <File>
              <FileName>module.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Lib\module.h</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lib\dma.c</FilePath>
            </File>
            <File>
              <FileName>dma.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Lib\dma.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Machine</GroupName>
          <Files>
            <File>
              <FileName>core_types.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Machine\core_types.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Myapp</GroupName>
          <Files>
            <File>
              <FileName>Mycommon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Myapp\Mycommon.c</FilePath>
            </File>
            <File>
              <FileName>Mycommon.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Myapp\Mycommon.h</FilePath>
            </File>
            <File>
              <FileName>common.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Myapp\common.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Fck_src</GroupName>
          <Files>
            <File>
              <FileName>lv_gui.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Fck_src\lv_gui.c</FilePath>
            </File>
            <File>
              <FileName>lv_gui.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Fck_src\lv_gui.h</FilePath>
            </File>
            <File>
              <FileName>PC_COM.C</FileName>
              <FileType>1</FileType>
              <FilePath>..\Fck_src\PC_COM.C</FilePath>
            </File>
            <File>
              <FileName>PC_COM.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Fck_src\PC_COM.h</FilePath>
            </File>
            <File>
              <FileName>Serial.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Fck_src\Serial.c</FilePath>
            </File>
            <File>
              <FileName>Serial.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Fck_src\Serial.h</FilePath>
            </File>
            <File>
              <FileName>ports_define.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Fck_src\ports_define.h</FilePath>
            </File>
            <File>
              <FileName>Meu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Fck_src\Meu.c</FilePath>
            </File>
            <File>
              <FileName>Meu.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Fck_src\Meu.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Wen_lib</GroupName>
          <Files>
            <File>
              <FileName>wen_type.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_type.h</FilePath>
            </File>
            <File>
              <FileName>wen_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Wen_lib\wen_event.c</FilePath>
            </File>
            <File>
              <FileName>wen_event.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_event.h</FilePath>
            </File>
            <File>
              <FileName>wen_lib.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_lib.h</FilePath>
            </File>
            <File>
              <FileName>wen_act.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Wen_lib\wen_act.c</FilePath>
            </File>
            <File>
              <FileName>wen_act.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_act.h</FilePath>
            </File>
            <File>
              <FileName>wen_fifo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Wen_lib\wen_fifo.c</FilePath>
            </File>
            <File>
              <FileName>wen_fifo.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_fifo.h</FilePath>
            </File>
            <File>
              <FileName>wen_interrupt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Wen_lib\wen_interrupt.c</FilePath>
            </File>
            <File>
              <FileName>wen_interrupt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_interrupt.h</FilePath>
            </File>
            <File>
              <FileName>wen_mem.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_mem.h</FilePath>
            </File>
            <File>
              <FileName>wen_stdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Wen_lib\wen_stdio.c</FilePath>
            </File>
            <File>
              <FileName>wen_stdio.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\Wen_lib\wen_stdio.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LibCRC</GroupName>
          <Files>
            <File>
              <FileName>checksum.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\LibCRC\checksum.h</FilePath>
            </File>
            <File>
              <FileName>crc8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\crc8.c</FilePath>
            </File>
            <File>
              <FileName>crc16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\crc16.c</FilePath>
            </File>
            <File>
              <FileName>crc32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\crc32.c</FilePath>
            </File>
            <File>
              <FileName>crcccitt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\crcccitt.c</FilePath>
            </File>
            <File>
              <FileName>crcccitt.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\LibCRC\crcccitt.h</FilePath>
            </File>
            <File>
              <FileName>crcdnp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\crcdnp.c</FilePath>
            </File>
            <File>
              <FileName>crckrmit.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\crckrmit.c</FilePath>
            </File>
            <File>
              <FileName>crcsick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\crcsick.c</FilePath>
            </File>
            <File>
              <FileName>nmea-chk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LibCRC\nmea-chk.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>mh-drivers</GroupName>
          <Files>
            <File>
              <FileName>peripheral.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\peripheral.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_usart.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_misc.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_gpio.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_dma.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_sysctrl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_sysctrl.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_i2c.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_exti.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_jpeg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_jpeg.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_ltdc.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_pwr.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_rtc.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_sdram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_sdram.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_tim.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_spi.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_qspi.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_cache.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_trng.c</FilePath>
            </File>
            <File>
              <FileName>mh245x_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\MH245x_StdPeriph_Driver\src\mh245x_iwdg.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FreeRTOS/src</GroupName>
          <Files>
            <File>
              <FileName>croutine.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\croutine.c</FilePath>
            </File>
            <File>
              <FileName>event_groups.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\event_groups.c</FilePath>
            </File>
            <File>
              <FileName>list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\list.c</FilePath>
            </File>
            <File>
              <FileName>queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\queue.c</FilePath>
            </File>
            <File>
              <FileName>tasks.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\tasks.c</FilePath>
            </File>
            <File>
              <FileName>timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\timers.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FreeRTOS/port</GroupName>
          <Files>
            <File>
              <FileName>port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\portable\GCC\ARM_CM4F\port.c</FilePath>
            </File>
            <File>
              <FileName>heap_4.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\RTOS\freertos\11.1.0\portable\MemMang\heap_4.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FreeRTOS/user</GroupName>
          <Files>
            <File>
              <FileName>rtos-freertos.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOSConfig\Config\rtos-freertos.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOSConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\FreeRTOSConfig\Config\FreeRTOSConfig.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl_core_v8</GroupName>
          <Files>
            <File>
              <FileName>lv_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_event.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_event.c</FilePath>
            </File>
            <File>
              <FileName>lv_group.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_group.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev_scroll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_indev_scroll.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_class.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj_class.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_draw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj_draw.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_pos.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj_pos.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_scroll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj_scroll.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_style_gen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj_style_gen.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj_tree.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_obj_tree.c</FilePath>
            </File>
            <File>
              <FileName>lv_refr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_refr.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\core\lv_theme.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_mask.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_mask.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_transform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_transform.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_draw_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_img_buf.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_img_cache.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\lv_img_decoder.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_blend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_blend.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_dither.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_dither.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_gradient.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_gradient.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_layer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_layer.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_letter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_letter.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_polygon.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_polygon.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_sw_transform.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\draw\sw\lv_draw_sw_transform.c</FilePath>
            </File>
            <File>
              <FileName>lv_flex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\layouts\flex\lv_flex.c</FilePath>
            </File>
            <File>
              <FileName>lv_grid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\layouts\grid\lv_grid.c</FilePath>
            </File>
            <File>
              <FileName>lv_bmp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\bmp\lv_bmp.c</FilePath>
            </File>
            <File>
              <FileName>lv_ffmpeg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\ffmpeg\lv_ffmpeg.c</FilePath>
            </File>
            <File>
              <FileName>lv_freetype.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\freetype\lv_freetype.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_fatfs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\fsdrv\lv_fs_fatfs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_littlefs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\fsdrv\lv_fs_littlefs.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_posix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\fsdrv\lv_fs_posix.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_stdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\fsdrv\lv_fs_stdio.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs_win32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\fsdrv\lv_fs_win32.c</FilePath>
            </File>
            <File>
              <FileName>gifdec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\gif\gifdec.c</FilePath>
            </File>
            <File>
              <FileName>lv_gif.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\gif\lv_gif.c</FilePath>
            </File>
            <File>
              <FileName>lodepng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\png\lodepng.c</FilePath>
            </File>
            <File>
              <FileName>lv_png.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\png\lv_png.c</FilePath>
            </File>
            <File>
              <FileName>lv_qrcode.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\qrcode\lv_qrcode.c</FilePath>
            </File>
            <File>
              <FileName>qrcodegen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\qrcode\qrcodegen.c</FilePath>
            </File>
            <File>
              <FileName>lv_rlottie.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\rlottie\lv_rlottie.c</FilePath>
            </File>
            <File>
              <FileName>lv_sjpg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\sjpg\lv_sjpg.c</FilePath>
            </File>
            <File>
              <FileName>tjpgd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\sjpg\tjpgd.c</FilePath>
            </File>
            <File>
              <FileName>lv_tiny_ttf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\libs\tiny_ttf\lv_tiny_ttf.c</FilePath>
            </File>
            <File>
              <FileName>lv_extra.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\lv_extra.c</FilePath>
            </File>
            <File>
              <FileName>lv_fragment.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\fragment\lv_fragment.c</FilePath>
            </File>
            <File>
              <FileName>lv_fragment_manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\fragment\lv_fragment_manager.c</FilePath>
            </File>
            <File>
              <FileName>lv_gridnav.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\gridnav\lv_gridnav.c</FilePath>
            </File>
            <File>
              <FileName>lv_ime_pinyin.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\ime\lv_ime_pinyin.c</FilePath>
            </File>
            <File>
              <FileName>lv_imgfont.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\imgfont\lv_imgfont.c</FilePath>
            </File>
            <File>
              <FileName>lv_monkey.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\monkey\lv_monkey.c</FilePath>
            </File>
            <File>
              <FileName>lv_msg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\msg\lv_msg.c</FilePath>
            </File>
            <File>
              <FileName>lv_snapshot.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\others\snapshot\lv_snapshot.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_basic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\themes\basic\lv_theme_basic.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_default.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\themes\default\lv_theme_default.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_mono.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\themes\mono\lv_theme_mono.c</FilePath>
            </File>
            <File>
              <FileName>lv_animimg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\animimg\lv_animimg.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\calendar\lv_calendar.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar_header_arrow.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\calendar\lv_calendar_header_arrow.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar_header_dropdown.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\calendar\lv_calendar_header_dropdown.c</FilePath>
            </File>
            <File>
              <FileName>lv_chart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\chart\lv_chart.c</FilePath>
            </File>
            <File>
              <FileName>lv_colorwheel.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\colorwheel\lv_colorwheel.c</FilePath>
            </File>
            <File>
              <FileName>lv_imgbtn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\imgbtn\lv_imgbtn.c</FilePath>
            </File>
            <File>
              <FileName>lv_keyboard.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\keyboard\lv_keyboard.c</FilePath>
            </File>
            <File>
              <FileName>lv_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\led\lv_led.c</FilePath>
            </File>
            <File>
              <FileName>lv_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\list\lv_list.c</FilePath>
            </File>
            <File>
              <FileName>lv_menu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\menu\lv_menu.c</FilePath>
            </File>
            <File>
              <FileName>lv_meter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\meter\lv_meter.c</FilePath>
            </File>
            <File>
              <FileName>lv_msgbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\msgbox\lv_msgbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_span.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\span\lv_span.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\spinbox\lv_spinbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinner.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\spinner\lv_spinner.c</FilePath>
            </File>
            <File>
              <FileName>lv_tabview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\tabview\lv_tabview.c</FilePath>
            </File>
            <File>
              <FileName>lv_tileview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\tileview\lv_tileview.c</FilePath>
            </File>
            <File>
              <FileName>lv_win.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\extra\widgets\win\lv_win.c</FilePath>
            </File>
            <File>
              <FileName>lv_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_dejavu_16_persian_hebrew.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_dejavu_16_persian_hebrew.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_fmt_txt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_fmt_txt.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_loader.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_loader.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_10.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_10.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_12.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_12_subpx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_12_subpx.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_14.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_14.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_18.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_18.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_20.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_22.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_22.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_24.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_24.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_26.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_26.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_28.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28_compressed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_28_compressed.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_30.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_30.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_32.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_34.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_34.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_36.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_36.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_38.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_38.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_40.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_40.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_42.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_42.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_44.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_44.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_46.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_46.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_48.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_montserrat_48.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_simsun_16_cjk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_simsun_16_cjk.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_unscii_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\font\lv_font_unscii_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\hal\lv_hal_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\hal\lv_hal_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_tick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\hal\lv_hal_tick.c</FilePath>
            </File>
            <File>
              <FileName>lv_anim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_anim.c</FilePath>
            </File>
            <File>
              <FileName>lv_anim_timeline.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_anim_timeline.c</FilePath>
            </File>
            <File>
              <FileName>lv_area.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_area.c</FilePath>
            </File>
            <File>
              <FileName>lv_async.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_async.c</FilePath>
            </File>
            <File>
              <FileName>lv_bidi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_bidi.c</FilePath>
            </File>
            <File>
              <FileName>lv_color.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_color.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_fs.c</FilePath>
            </File>
            <File>
              <FileName>lv_gc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_gc.c</FilePath>
            </File>
            <File>
              <FileName>lv_ll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_ll.c</FilePath>
            </File>
            <File>
              <FileName>lv_log.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_log.c</FilePath>
            </File>
            <File>
              <FileName>lv_lru.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_lru.c</FilePath>
            </File>
            <File>
              <FileName>lv_math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_math.c</FilePath>
            </File>
            <File>
              <FileName>lv_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_mem.c</FilePath>
            </File>
            <File>
              <FileName>lv_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_printf.c</FilePath>
            </File>
            <File>
              <FileName>lv_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_style_gen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_style_gen.c</FilePath>
            </File>
            <File>
              <FileName>lv_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_timer.c</FilePath>
            </File>
            <File>
              <FileName>lv_tlsf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_tlsf.c</FilePath>
            </File>
            <File>
              <FileName>lv_txt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_txt.c</FilePath>
            </File>
            <File>
              <FileName>lv_txt_ap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_txt_ap.c</FilePath>
            </File>
            <File>
              <FileName>lv_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\misc\lv_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_bar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_bar.c</FilePath>
            </File>
            <File>
              <FileName>lv_btn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_btn.c</FilePath>
            </File>
            <File>
              <FileName>lv_btnmatrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_btnmatrix.c</FilePath>
            </File>
            <File>
              <FileName>lv_canvas.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_canvas.c</FilePath>
            </File>
            <File>
              <FileName>lv_checkbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_checkbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_dropdown.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_dropdown.c</FilePath>
            </File>
            <File>
              <FileName>lv_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_objx_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_objx_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_roller.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_roller.c</FilePath>
            </File>
            <File>
              <FileName>lv_slider.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_slider.c</FilePath>
            </File>
            <File>
              <FileName>lv_switch.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_switch.c</FilePath>
            </File>
            <File>
              <FileName>lv_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_table.c</FilePath>
            </File>
            <File>
              <FileName>lv_textarea.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\src\widgets\lv_textarea.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl-demo-v8</GroupName>
          <Files>
            <File>
              <FileName>img_clothes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\demos\widgets\assets\img_clothes.c</FilePath>
            </File>
            <File>
              <FileName>img_demo_widgets_avatar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\demos\widgets\assets\img_demo_widgets_avatar.c</FilePath>
            </File>
            <File>
              <FileName>img_lvgl_logo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\demos\widgets\assets\img_lvgl_logo.c</FilePath>
            </File>
            <File>
              <FileName>lv_demo_widgets.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Libraries\OSS\lvgl\8.4.0\demos\widgets\lv_demo_widgets.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl/Porting</GroupName>
          <Files>
            <File>
              <FileName>lv_port_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LVGL\lv_port_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_port_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LVGL\lv_port_indev.c</FilePath>
            </File>
            <File>
              <FileName>lvgl_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\LVGL\lvgl_config.c</FilePath>
            </File>
            <File>
              <FileName>lv_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\LVGL\lv_conf.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>BSP</GroupName>
          <Files>
            <File>
              <FileName>bsp_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_led\bsp_led.c</FilePath>
            </File>
            <File>
              <FileName>bsp_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_rtc\bsp_rtc.c</FilePath>
            </File>
            <File>
              <FileName>bsp_sdram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_sdram\bsp_sdram.c</FilePath>
            </File>
            <File>
              <FileName>bsp_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_usart\bsp_usart.c</FilePath>
            </File>
            <File>
              <FileName>bsp_rgb_lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_lcd\bsp_rgb_lcd.c</FilePath>
            </File>
            <File>
              <FileName>bsp_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_trng\bsp_trng.c</FilePath>
            </File>
            <File>
              <FileName>bsp_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_iwdg\bsp_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>bsp_SysTick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_systick\bsp_SysTick.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>LCD</GroupName>
          <Files>
            <File>
              <FileName>lcd_rgb_PM_360x960.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_lcd\lcd_rgb_PM_360x960.c</FilePath>
            </File>
            <File>
              <FileName>lcd_rgb_4_3inch_800x480.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_lcd\lcd_rgb_4_3inch_800x480.c</FilePath>
            </File>
            <File>
              <FileName>lcd_rgb_5inch_800x480.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_lcd\lcd_rgb_5inch_800x480.c</FilePath>
            </File>
            <File>
              <FileName>lcd_rgb_7inch_1024x600.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_lcd\lcd_rgb_7inch_1024x600.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Touch</GroupName>
          <Files>
            <File>
              <FileName>ctiic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_touch\ctiic.c</FilePath>
            </File>
            <File>
              <FileName>touch_goodix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\BSP\bsp_touch\touch_goodix.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>rtc-alarm</LayName>
        <LayTarg>0</LayTarg>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
