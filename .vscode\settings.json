{"files.associations": {"PC_COM.C": "cpp", "bsp_usart.h": "c", "app_main.h": "c", "string.h": "c", "stdint.h": "c", "rtos-freertos.h": "c", "hx_proto.h": "c", "bsp_led.h": "c", "lv_port_disp.h": "c", "font_example.h": "c", "serial.h": "c", "task.h": "c", "freertos.h": "c", "diskio.h": "c", "mh2457.h": "c", "437_80x40.h": "c", "wheap.h": "c", "condition_variable": "c", "usart3.h": "c", "lvgl.h": "c", "wen_lib.h": "c", "hw.h": "c", "lv_gui.h": "c", "lv_demos.h": "c", "ff.h": "c", "lv_conf_internal.h": "c", "lv_symbol_def.h": "c", "lv_draw_sw.h": "c", "lv_assert.h": "c", "cstddef": "cpp", "string": "cpp", "optional": "cpp", "limits": "cpp", "memory": "cpp", "new": "cpp", "functional": "cpp", "type_traits": "cpp", "fatfs_test.h": "c", "bsp_rgb_lcd.h": "c", "lv_math.h": "c", "stdlib.h": "c", "lcd_rgb_pm_360x960.h": "c", "stdio.h": "c", "wen_event.h": "c", "keyboard.h": "c", "pc_com.h": "c", "meu.h": "c", "mh245x_trng.h": "c", "mh245x_cache.h": "c", "peripheral.h": "c", "bsp_iwdg.h": "c", "run_led.h": "c", "checksum.h": "c", "bsp_systick.h": "c", "lfs.h": "c", "bsp_sdram.h": "c", "mh245x_qspi.h": "c", "lfs_util.h": "c", "mutex": "c", "thread": "c", "track_fun_aes.h": "c", "tlsf.h": "c", "picture.h": "c", "malloc.h": "c", "mycommon.h": "c", "lvgl_config.h": "c", "wen_type.h": "c", "jdev.h": "c", "lv_color.h": "c", "lv_obj.h": "c", "lfs_speed_test.h": "c", "lv_area.h": "c", "tjpgd.h": "c", "dma.h": "c", "lv_types.h": "c", "ports_define.h": "c", "lv_img.h": "c", "sstream": "c", "regex": "c", "ui_helpers.h": "c", "common.h": "c", "stdbool.h": "c", "bitset": "c", "chrono": "c", "algorithm": "c", "streambuf": "c", "array": "c", "deque": "c", "list": "c", "unordered_map": "c", "vector": "c", "string_view": "c", "initializer_list": "c", "utility": "c", "typeinfo": "c"}}